
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> src</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">89.74% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>831/926</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">82.83% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>222/268</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">92.81% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>142/153</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">89.51% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>768/858</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="financial-projection-table.ts"><a href="financial-projection-table.ts.html">financial-projection-table.ts</a></td>
	<td data-value="95.32" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 95%"></div><div class="cover-empty" style="width: 5%"></div></div>
	</td>
	<td data-value="95.32" class="pct high">95.32%</td>
	<td data-value="107" class="abs high">102/107</td>
	<td data-value="81.48" class="pct medium">81.48%</td>
	<td data-value="27" class="abs medium">22/27</td>
	<td data-value="87.5" class="pct medium">87.5%</td>
	<td data-value="24" class="abs medium">21/24</td>
	<td data-value="96.77" class="pct high">96.77%</td>
	<td data-value="93" class="abs high">90/93</td>
	</tr>

<tr>
	<td class="file high" data-value="formulas.ts"><a href="formulas.ts.html">formulas.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="137" class="abs high">137/137</td>
	<td data-value="96.15" class="pct high">96.15%</td>
	<td data-value="26" class="abs high">25/26</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="20" class="abs high">20/20</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="132" class="abs high">132/132</td>
	</tr>

<tr>
	<td class="file high" data-value="payslip-parser.ts"><a href="payslip-parser.ts.html">payslip-parser.ts</a></td>
	<td data-value="90.97" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 90%"></div><div class="cover-empty" style="width: 10%"></div></div>
	</td>
	<td data-value="90.97" class="pct high">90.97%</td>
	<td data-value="133" class="abs high">121/133</td>
	<td data-value="85.71" class="pct medium">85.71%</td>
	<td data-value="70" class="abs medium">60/70</td>
	<td data-value="95" class="pct high">95%</td>
	<td data-value="20" class="abs high">19/20</td>
	<td data-value="90.35" class="pct high">90.35%</td>
	<td data-value="114" class="abs high">103/114</td>
	</tr>

<tr>
	<td class="file high" data-value="portfolio-parser.ts"><a href="portfolio-parser.ts.html">portfolio-parser.ts</a></td>
	<td data-value="94.33" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 94%"></div><div class="cover-empty" style="width: 6%"></div></div>
	</td>
	<td data-value="94.33" class="pct high">94.33%</td>
	<td data-value="106" class="abs high">100/106</td>
	<td data-value="85.36" class="pct medium">85.36%</td>
	<td data-value="41" class="abs medium">35/41</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="10" class="abs high">10/10</td>
	<td data-value="94" class="pct high">94%</td>
	<td data-value="100" class="abs high">94/100</td>
	</tr>

<tr>
	<td class="file low" data-value="test-projection-table.ts"><a href="test-projection-table.ts.html">test-projection-table.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	</tr>

<tr>
	<td class="file high" data-value="types.ts"><a href="types.ts.html">types.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	</tr>

<tr>
	<td class="file medium" data-value="wealth-tracker.ts"><a href="wealth-tracker.ts.html">wealth-tracker.ts</a></td>
	<td data-value="85.25" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 85%"></div><div class="cover-empty" style="width: 15%"></div></div>
	</td>
	<td data-value="85.25" class="pct medium">85.25%</td>
	<td data-value="434" class="abs medium">370/434</td>
	<td data-value="76.92" class="pct medium">76.92%</td>
	<td data-value="104" class="abs medium">80/104</td>
	<td data-value="92.3" class="pct high">92.3%</td>
	<td data-value="78" class="abs high">72/78</td>
	<td data-value="84.87" class="pct medium">84.87%</td>
	<td data-value="410" class="abs medium">348/410</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-17T17:23:23.274Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    