
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">80.41% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>1117/1389</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">75.62% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>304/402</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">78.57% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>198/252</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">79.83% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>1049/1314</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="src"><a href="src/index.html">src</a></td>
	<td data-value="89.74" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 89%"></div><div class="cover-empty" style="width: 11%"></div></div>
	</td>
	<td data-value="89.74" class="pct medium">89.74%</td>
	<td data-value="926" class="abs medium">831/926</td>
	<td data-value="82.83" class="pct medium">82.83%</td>
	<td data-value="268" class="abs medium">222/268</td>
	<td data-value="92.81" class="pct high">92.81%</td>
	<td data-value="153" class="abs high">142/153</td>
	<td data-value="89.51" class="pct medium">89.51%</td>
	<td data-value="858" class="abs medium">768/858</td>
	</tr>

<tr>
	<td class="file low" data-value="src/__tests__"><a href="src/__tests__/index.html">src/__tests__</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="141" class="abs low">0/141</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="39" class="abs low">0/39</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="139" class="abs low">0/139</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/__tests__/data"><a href="src/__tests__/data/index.html">src/__tests__/data</a></td>
	<td data-value="66.66" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 66%"></div><div class="cover-empty" style="width: 34%"></div></div>
	</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="6" class="abs medium">4/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="6" class="abs medium">4/6</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/api"><a href="src/api/index.html">src/api</a></td>
	<td data-value="86.3" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 86%"></div><div class="cover-empty" style="width: 14%"></div></div>
	</td>
	<td data-value="86.3" class="pct medium">86.3%</td>
	<td data-value="219" class="abs medium">189/219</td>
	<td data-value="59.32" class="pct medium">59.32%</td>
	<td data-value="118" class="abs medium">70/118</td>
	<td data-value="91.17" class="pct high">91.17%</td>
	<td data-value="34" class="abs high">31/34</td>
	<td data-value="86.11" class="pct medium">86.11%</td>
	<td data-value="216" class="abs medium">186/216</td>
	</tr>

<tr>
	<td class="file high" data-value="src/data"><a href="src/data/index.html">src/data</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	</tr>

<tr>
	<td class="file high" data-value="src/services"><a href="src/services/index.html">src/services</a></td>
	<td data-value="95.74" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 95%"></div><div class="cover-empty" style="width: 5%"></div></div>
	</td>
	<td data-value="95.74" class="pct high">95.74%</td>
	<td data-value="94" class="abs high">90/94</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="16" class="abs medium">12/16</td>
	<td data-value="96.15" class="pct high">96.15%</td>
	<td data-value="26" class="abs high">25/26</td>
	<td data-value="95.65" class="pct high">95.65%</td>
	<td data-value="92" class="abs high">88/92</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-17T17:23:23.274Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    