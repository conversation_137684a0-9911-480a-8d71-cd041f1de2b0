import request from 'supertest';
import app from '../api/server';

describe('API Server', () => {
  describe('Health Check', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'healthy');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('uptime');
    });
  });

  describe('Configuration Endpoints', () => {
    it('should get configuration', async () => {
      const response = await request(app)
        .get('/api/config')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('wealthTargets');
      expect(response.body.data).toHaveProperty('defaultTarget');
    });

    it('should update configuration', async () => {
      const newConfig = {
        defaultTarget: 2000000,
        defaultMonthlyContributions: [5000, 6000, 7000]
      };

      const response = await request(app)
        .put('/api/config')
        .send(newConfig)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Configuration updated');
    });
  });

  describe('Portfolio Endpoints', () => {
    it('should add portfolio data', async () => {
      const portfolioData = {
        year: 2024,
        month: 7,
        trow: 400000,
        robinhood: 200000,
        etrade: 150000,
        teradata: 50000,
        fidelity: 0
      };

      const response = await request(app)
        .post('/api/portfolio')
        .send(portfolioData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Portfolio data added');
    });

    it('should reject incomplete portfolio data', async () => {
      const incompleteData = {
        year: 2024,
        month: 7,
        trow: 400000
        // Missing required fields
      };

      const response = await request(app)
        .post('/api/portfolio')
        .send(incompleteData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Missing required fields');
    });

    it('should get recent portfolio data', async () => {
      const response = await request(app)
        .get('/api/portfolio/recent')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('date');
    });
  });

  describe('Payslip Endpoints', () => {
    it('should add payslip data', async () => {
      const payslipData = {
        date: '2024-07-15',
        gross: 8000,
        net: 6000,
        espp: 1000,
        rothE: 500,
        rothR: 500
      };

      const response = await request(app)
        .post('/api/payslips')
        .send(payslipData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Payslip data added');
    });

    it('should reject incomplete payslip data', async () => {
      const incompleteData = {
        date: '2024-07-15',
        gross: 8000
        // Missing required fields
      };

      const response = await request(app)
        .post('/api/payslips')
        .send(incompleteData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Missing required fields');
    });

    it('should get payslip data', async () => {
      const response = await request(app)
        .get('/api/payslips?count=5')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });

  describe('Analysis Endpoints', () => {
    beforeEach(async () => {
      // Add sufficient data for analysis
      await request(app)
        .post('/api/portfolio')
        .send({
          year: 2024,
          month: 1,
          trow: 300000,
          robinhood: 150000,
          etrade: 100000,
          teradata: 50000
        });

      await request(app)
        .post('/api/portfolio')
        .send({
          year: 2024,
          month: 7,
          trow: 400000,
          robinhood: 200000,
          etrade: 150000,
          teradata: 50000
        });

      await request(app)
        .post('/api/payslips')
        .send({
          date: '2024-07-15',
          gross: 8000,
          net: 6000,
          espp: 1000,
          rothE: 500,
          rothR: 500
        });
    });

    it('should perform basic analysis', async () => {
      const response = await request(app)
        .get('/api/analysis/basic')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('currentPortfolioValue');
      expect(response.body.data).toHaveProperty('monthlyContribution');
      expect(response.body.data).toHaveProperty('irr');
    });

    it('should generate projections', async () => {
      const response = await request(app)
        .get('/api/analysis/projections?target=1500000&contributions=5000,6000,7000')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('projections');
      expect(Array.isArray(response.body.data.projections)).toBe(true);
    });

    it('should generate milestone projections', async () => {
      const response = await request(app)
        .get('/api/analysis/milestone-projections?target=1500000&contributions=4200,5260,6250')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('target');
      expect(response.body.data).toHaveProperty('currentValue');
      expect(response.body.data).toHaveProperty('projections');
      expect(Array.isArray(response.body.data.projections)).toBe(true);
    });

    it('should reject analysis with insufficient data', async () => {
      // Reset data first
      await request(app)
        .post('/api/data/reset')
        .expect(200);

      const response = await request(app)
        .get('/api/analysis/basic')
        .expect(400); // Now expects 400 since reset properly clears all data

      // Since reset now clears all data, insufficient data should be detected
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Insufficient data');
    });
  });

  describe('Data Management Endpoints', () => {
    it('should reset data', async () => {
      const response = await request(app)
        .post('/api/data/reset')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Data reset successfully');
    });

    it('should clear and setup data', async () => {
      const setupData = {
        portfolioValue: 800000,
        monthlyContribution: 6250
      };

      const response = await request(app)
        .post('/api/data/clear-and-setup')
        .send(setupData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Data cleared and initial setup completed');
      expect(response.body.data).toHaveProperty('portfolioValue', 800000);
      expect(response.body.data).toHaveProperty('monthlyContribution', 6250);
    });

    it('should reject clear and setup with missing data', async () => {
      const incompleteData = {
        portfolioValue: 800000
        // Missing monthlyContribution
      };

      const response = await request(app)
        .post('/api/data/clear-and-setup')
        .send(incompleteData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Missing required fields');
    });

    it('should reject clear and setup with negative values', async () => {
      const invalidData = {
        portfolioValue: -1000,
        monthlyContribution: 6250
      };

      const response = await request(app)
        .post('/api/data/clear-and-setup')
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('must be non-negative');
    });
  });

  describe('Error Handling', () => {
    it('should handle 404 for unknown endpoints', async () => {
      const response = await request(app)
        .get('/api/unknown-endpoint')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Endpoint not found');
    });

    it('should handle invalid JSON in request body', async () => {
      const response = await request(app)
        .post('/api/portfolio')
        .send('invalid json')
        .set('Content-Type', 'application/json')
        .expect(400); // Now properly handled with 400 status

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid JSON format');
      expect(response.body.message).toBe('Request body must be valid JSON');
    });
  });
});
