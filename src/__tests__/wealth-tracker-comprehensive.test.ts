import { WealthTracker } from '../wealth-tracker';
import * as fs from 'fs';
import * as readline from 'readline';

// Mock fs module
jest.mock('fs');
const mockedFs = fs as jest.Mocked<typeof fs>;

// Mock readline module
jest.mock('readline');

describe('WealthTracker Comprehensive Tests', () => {
  let tracker: WealthTracker;

  beforeEach(() => {
    jest.clearAllMocks();
    tracker = new WealthTracker();
  });

  describe('Constructor and Configuration', () => {
    it('should initialize with default config', () => {
      const config = tracker.getConfig();
      expect(config.wealthTargets).toEqual([900000, 1500000, 2000000, 3000000]);
      expect(config.defaultTarget).toBe(1500000);
      expect(config.paychecksPerYear).toBe(26);
    });

    it('should initialize with custom config', () => {
      const customConfig = {
        defaultTarget: 2000000,
        paychecksPerYear: 24,
        monthlyExpenses: 3500
      };
      
      const customTracker = new WealthTracker(customConfig);
      const config = customTracker.getConfig();
      
      expect(config.defaultTarget).toBe(2000000);
      expect(config.paychecksPerYear).toBe(24);
      expect(config.monthlyExpenses).toBe(3500);
    });

    it('should update wealth targets', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const newTargets = [1000000, 2000000, 5000000];
      
      tracker.setWealthTargets(newTargets);
      
      const config = tracker.getConfig();
      expect(config.wealthTargets).toEqual(newTargets);
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Wealth targets updated'));
      
      consoleSpy.mockRestore();
    });

    it('should update default monthly contributions', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const newContributions = [4000, 5000, 6000, 7000];
      
      tracker.setDefaultMonthlyContributions(newContributions);
      
      const config = tracker.getConfig();
      expect(config.defaultMonthlyContributions).toEqual(newContributions);
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Default contributions updated'));
      
      consoleSpy.mockRestore();
    });

    it('should update target amount', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      tracker.setTargetAmount(2500000);
      
      const config = tracker.getConfig();
      expect(config.defaultTarget).toBe(2500000);
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Default target updated to $2.5M'));
      
      consoleSpy.mockRestore();
    });
  });

  describe('Portfolio Data Management', () => {
    it('should add portfolio data with all fields', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      tracker.clearAllData(); // Clear initial data
      
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000, 100000);
      
      const history = tracker.getPortfolioHistory();
      expect(history).toHaveLength(1);
      expect(history[0]).toEqual({
        date: '2024-07',
        trow: 400000,
        robinhood: 200000,
        etrade: 150000,
        teradata: 50000,
        fidelity: 100000
      });
      
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Added portfolio data for'));
      consoleSpy.mockRestore();
    });

    it('should add portfolio data without fidelity', () => {
      tracker.clearAllData(); // Clear initial data
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      
      const entry = tracker.getMostRecentPortfolioEntry();
      expect(entry).toBeDefined();
      expect(entry!.fidelity).toBe(0);
    });

    it('should handle addPortfolioDataIfComplete with valid data', () => {
      tracker.clearAllData(); // Clear initial data
      const result = tracker.addPortfolioDataIfComplete(2024, 7, 400000, 200000, 150000, 50000, 100000);
      
      expect(result).toBe(true);
      expect(tracker.getPortfolioHistory()).toHaveLength(1);
    });

    it('should reject incomplete portfolio data', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      tracker.clearAllData(); // Clear initial data
      
      const result = tracker.addPortfolioDataIfComplete(undefined, 7, 400000, 200000, 150000, 50000);
      
      expect(result).toBe(false);
      expect(tracker.getPortfolioHistory()).toHaveLength(0);
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Skipping portfolio data addition'));
      
      consoleSpy.mockRestore();
    });

    it('should reject invalid year range', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      const result = tracker.addPortfolioDataIfComplete(2019, 7, 400000, 200000, 150000, 50000);
      
      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('invalid year or month range'));
      
      consoleSpy.mockRestore();
    });

    it('should reject invalid month range', () => {
      const result = tracker.addPortfolioDataIfComplete(2024, 13, 400000, 200000, 150000, 50000);
      
      expect(result).toBe(false);
    });

    it('should get portfolio history copy', () => {
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      
      const history1 = tracker.getPortfolioHistory();
      const history2 = tracker.getPortfolioHistory();
      
      expect(history1).not.toBe(history2); // Different references
      expect(history1).toEqual(history2); // Same content
    });

    it('should return null for empty portfolio', () => {
      tracker.clearAllData(); // Clear initial data
      const recent = tracker.getMostRecentPortfolioEntry();
      expect(recent).toBeNull();
    });
  });

  describe('Payslip Data Management', () => {
    it('should add payslip data', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      tracker.clearAllData(); // Clear initial data
      
      tracker.addPayslip('2024-07-15', 8000, 6000, 800, 400, 400);
      
      const payslips = tracker.getMostRecentPayslips(1);
      expect(payslips).toHaveLength(1);
      expect(payslips[0]).toEqual({
        date: '2024-07-15',
        gross: 8000,
        net: 6000,
        espp: 800,
        roth_e: 400,
        roth_r: 400,
        total_invest: 1600
      });
      
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Added payslip for'));
      consoleSpy.mockRestore();
    });

    it('should sort payslips by date', () => {
      tracker.clearAllData(); // Clear initial data
      tracker.addPayslip('2024-07-20', 8000, 6000, 800, 400, 400);
      tracker.addPayslip('2024-07-10', 8000, 6000, 800, 400, 400);
      tracker.addPayslip('2024-07-15', 8000, 6000, 800, 400, 400);
      
      const history = tracker.getPayslipHistory();
      expect(history[0].date).toBe('2024-07-10');
      expect(history[1].date).toBe('2024-07-15');
      expect(history[2].date).toBe('2024-07-20');
    });

    it('should get limited recent payslips', () => {
      tracker.clearAllData(); // Clear initial data
      for (let i = 1; i <= 5; i++) {
        tracker.addPayslip(`2024-07-${i.toString().padStart(2, '0')}`, 8000, 6000, 800, 400, 400);
      }
      
      const recent = tracker.getMostRecentPayslips(3);
      expect(recent).toHaveLength(3);
      expect(recent[0].date).toBe('2024-07-03');
    });

    it('should return empty array for no payslips', () => {
      tracker.clearAllData(); // Clear initial data
      const recent = tracker.getMostRecentPayslips(5);
      expect(recent).toEqual([]);
    });

    it('should return all payslips when requested count exceeds total', () => {
      tracker.clearAllData(); // Clear initial data
      tracker.addPayslip('2024-07-15', 8000, 6000, 800, 400, 400);
      tracker.addPayslip('2024-07-30', 8000, 6000, 800, 400, 400);
      
      const recent = tracker.getMostRecentPayslips(10);
      expect(recent).toHaveLength(2);
    });

    it('should get payslip history copy', () => {
      tracker.addPayslip('2024-07-15', 8000, 6000, 800, 400, 400);
      
      const history1 = tracker.getPayslipHistory();
      const history2 = tracker.getPayslipHistory();
      
      expect(history1).not.toBe(history2); // Different references
      expect(history1).toEqual(history2); // Same content
    });
  });

  describe('Data Analysis', () => {
    beforeEach(() => {
      // Add sample data
      tracker.addPortfolioData(2024, 1, 300000, 150000, 100000, 50000);
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      tracker.addPayslip('2024-07-15', 8000, 6000, 800, 400, 400);
    });

    it('should check sufficient data for analysis', () => {
      expect(tracker.hasSufficientDataForAnalysis()).toBe(true);
      
      const emptyTracker = new WealthTracker();
      emptyTracker.clearAllData(); // Clear initial data
      expect(emptyTracker.hasSufficientDataForAnalysis()).toBe(false);
      
      const portfolioOnlyTracker = new WealthTracker();
      portfolioOnlyTracker.clearAllData(); // Clear initial data
      portfolioOnlyTracker.addPortfolioData(2024, 7, 100000, 50000, 30000, 20000);
      expect(portfolioOnlyTracker.hasSufficientDataForAnalysis()).toBe(false);
      
      const payslipOnlyTracker = new WealthTracker();
      payslipOnlyTracker.clearAllData(); // Clear initial data
      payslipOnlyTracker.addPayslip('2024-07-15', 8000, 6000, 800, 400, 400);
      expect(payslipOnlyTracker.hasSufficientDataForAnalysis()).toBe(false);
    });

    it('should calculate average monthly contribution from recent payslips', () => {
      // Add more recent payslips
      const today = new Date();
      for (let i = 0; i < 6; i++) {
        const date = new Date(today);
        date.setMonth(date.getMonth() - i);
        tracker.addPayslip(date.toISOString().split('T')[0], 8000, 6000, 800, 400, 400);
      }
      
      const contribution = tracker.calculateAverageMonthlyContribution();
      expect(contribution).toBeGreaterThan(0);
    });

    it('should use fallback contribution when no payslips', () => {
      const emptyTracker = new WealthTracker();
      emptyTracker.clearAllData(); // Clear initial data
      const contribution = emptyTracker.calculateAverageMonthlyContribution();
      expect(contribution).toBe(4000); // fallback value
    });

    it('should calculate IRR', () => {
      const irr = tracker.calculateIrr();
      expect(typeof irr).toBe('number');
      expect(irr).toBeGreaterThan(-1);
      expect(irr).toBeLessThan(1);
    });

    it('should calculate period returns', () => {
      const returns = tracker.calculatePeriodReturns();
      expect(Array.isArray(returns)).toBe(true);
      expect(returns.length).toBeGreaterThan(0);
    });

    it('should calculate statistics', () => {
      const stats = tracker.calculateStatistics();
      expect(stats).toHaveProperty('meanReturn');
      expect(stats).toHaveProperty('stdDev');
      expect(typeof stats.meanReturn).toBe('number');
      expect(typeof stats.stdDev).toBe('number');
    });

    it('should project future', () => {
      const months = tracker.projectFuture(2000000, 0.02, 6000);
      expect(months).toBeGreaterThan(0);
      expect(Number.isInteger(months)).toBe(true);
    });

    it('should analyze required contributions', () => {
      const analysis = tracker.analyzeRequiredContributions();
      expect(analysis).toHaveProperty('targets');
      expect(Array.isArray(analysis.targets)).toBe(true);
      expect(analysis.targets[0]).toHaveProperty('amount');
      expect(analysis.targets[0]).toHaveProperty('timeframes');
    });

    it('should analyze component split', () => {
      const analysis = tracker.analyzeComponentSplit();
      expect(analysis).toHaveProperty('projections');
      expect(Array.isArray(analysis.projections)).toBe(true);
      expect(analysis.projections[0]).toHaveProperty('scenario');
      expect(analysis.projections[0]).toHaveProperty('FV_total');
    });

    it('should analyze coast fire', () => {
      const analysis = tracker.analyzeCoastFire();
      expect(analysis).toHaveProperty('thresholds');
      expect(analysis).toHaveProperty('currentStatus');
      expect(analysis).toHaveProperty('monthsToCoastFire');
    });
  });

  describe('Wealth Calculator Integration', () => {
    it('should get wealth calculator instance', () => {
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      tracker.addPayslip('2024-07-15', 8000, 6000, 800, 400, 400);
      
      const calculator1 = tracker.getWealthCalculator();
      const calculator2 = tracker.getWealthCalculator();
      
      expect(calculator1).toBe(calculator2); // Same instance
    });
  });

  describe('Data Persistence', () => {
    it('should save data to file', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      mockedFs.writeFileSync.mockImplementation(() => {});
      
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      tracker.addPayslip('2024-07-15', 8000, 6000, 800, 400, 400);
      
      tracker.saveData('test_backup.json');
      
      expect(mockedFs.writeFileSync).toHaveBeenCalledWith(
        'test_backup.json',
        expect.stringContaining('portfolio_history')
      );
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Data saved to test_backup.json'));
      
      consoleSpy.mockRestore();
    });

    it('should save data with default filename', () => {
      mockedFs.writeFileSync.mockImplementation(() => {});
      
      tracker.saveData();
      
      expect(mockedFs.writeFileSync).toHaveBeenCalledWith(
        'wealth_tracker_data.json',
        expect.any(String)
      );
    });

    it('should load data from file', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const mockData = {
        portfolio_history: [
          { date: '2024-07', trow: 400000, robinhood: 200000, etrade: 150000, teradata: 50000 }
        ],
        payslip_history: [
          { date: '2024-07-15', gross: 8000, net: 6000, espp: 800, roth_e: 400, roth_r: 400, total_invest: 1600 }
        ]
      };
      
      mockedFs.readFileSync.mockReturnValue(JSON.stringify(mockData));
      
      tracker.loadData('test_backup.json');
      
      expect(tracker.getPortfolioHistory()).toHaveLength(1);
      expect(tracker.getPayslipHistory()).toHaveLength(1);
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Data loaded from test_backup.json'));
      
      consoleSpy.mockRestore();
    });

    it('should handle load error gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      mockedFs.readFileSync.mockImplementation(() => {
        throw new Error('File not found');
      });
      
      tracker.loadData('nonexistent.json');
      
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('No saved data found'));
      
      consoleSpy.mockRestore();
    });
  });

  describe('Display Methods', () => {
    beforeEach(() => {
      // Clear initial data and add sample data for display tests
      tracker.clearAllData();
      tracker.addPortfolioData(2024, 1, 300000, 150000, 100000, 50000);
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      tracker.addPayslip('2024-07-15', 8000, 6000, 800, 400, 400);
    });

    it('should show portfolio history', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      tracker.showPortfolioHistory();
      
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('PORTFOLIO HISTORY'));
      // Check that some portfolio data is displayed
      expect(consoleSpy.mock.calls.length).toBeGreaterThan(3);
      
      consoleSpy.mockRestore();
    });

    it('should show recent payslips', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      tracker.showRecentPayslips();
      
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('RECENT PAYSLIPS'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('2024-07-15'));
      
      consoleSpy.mockRestore();
    });

    it('should analyze payslips', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      tracker.analyzePayslips();
      
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('PAYSLIP ANALYSIS'));
      
      consoleSpy.mockRestore();
    });

    it('should show empty payslip analysis', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const emptyTracker = new WealthTracker();
      emptyTracker.clearAllData(); // Clear initial data
      
      emptyTracker.analyzePayslips();
      
      expect(consoleSpy).toHaveBeenCalledWith('No payslip data available.');
      
      consoleSpy.mockRestore();
    });
  });

  describe('Interactive Methods', () => {
    it('should handle addRecentPayslips interaction', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      tracker.clearAllData(); // Clear initial data
      
      // Mock readline interface
      const mockQuestion = jest.fn()
        .mockImplementationOnce((q, cb) => cb('2024-07-15'))
        .mockImplementationOnce((q, cb) => cb('8000'))
        .mockImplementationOnce((q, cb) => cb('6000'))
        .mockImplementationOnce((q, cb) => cb('800'))
        .mockImplementationOnce((q, cb) => cb('400'))
        .mockImplementationOnce((q, cb) => cb('400'))
        .mockImplementationOnce((q, cb) => cb('')); // Empty to finish
      
      const mockClose = jest.fn();
      
      (readline.createInterface as jest.Mock).mockReturnValue({
        question: mockQuestion,
        close: mockClose
      });
      
      await tracker.addRecentPayslips();
      
      expect(tracker.getPayslipHistory()).toHaveLength(1);
      expect(mockClose).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });

    it.skip('should handle invalid input in addRecentPayslips', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      tracker.clearAllData(); // Clear initial data
      
      const mockQuestion = jest.fn()
        .mockImplementationOnce((q, cb) => setTimeout(() => cb('2024-07-15'), 0))
        .mockImplementationOnce((q, cb) => setTimeout(() => cb('invalid'), 0))
        .mockImplementationOnce((q, cb) => setTimeout(() => cb(''), 0)); // Empty to finish
      
      const mockClose = jest.fn();
      
      (readline.createInterface as jest.Mock).mockReturnValue({
        question: mockQuestion,
        close: mockClose
      });
      
      await tracker.addRecentPayslips();
      
      expect(consoleSpy).toHaveBeenCalledWith('Invalid input. Please enter numbers only.');
      
      consoleSpy.mockRestore();
    }, 10000);
  });

  describe('Edge Cases', () => {
    it('should handle negative portfolio values', () => {
      tracker.addPortfolioData(2024, 7, -100000, 200000, 150000, 50000);
      
      const entry = tracker.getMostRecentPortfolioEntry();
      expect(entry).toBeDefined();
      expect(entry!.trow).toBe(-100000);
    });

    it('should handle extreme dates', () => {
      tracker.clearAllData(); // Clear initial data
      tracker.addPortfolioData(1900, 1, 100000, 50000, 30000, 20000);
      tracker.addPortfolioData(2100, 12, 200000, 100000, 60000, 40000);
      
      const history = tracker.getPortfolioHistory();
      expect(history).toHaveLength(2);
    });

    it('should handle very large values', () => {
      tracker.addPortfolioData(2024, 7, 1e10, 1e9, 1e8, 1e7);
      
      const entry = tracker.getMostRecentPortfolioEntry();
      expect(entry).toBeDefined();
      expect(entry!.trow).toBe(1e10);
    });

    it('should handle multiple entries in same month', () => {
      tracker.clearAllData(); // Clear initial data
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      tracker.addPortfolioData(2024, 7, 450000, 220000, 160000, 55000);
      
      const history = tracker.getPortfolioHistory();
      expect(history).toHaveLength(2);
      
      const recent = tracker.getMostRecentPortfolioEntry();
      expect(recent!.trow).toBe(450000);
    });
  });
});