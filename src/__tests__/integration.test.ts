import request from 'supertest';
import app from '../api/server';
import { WealthTracker } from '../wealth-tracker';

describe('Integration Tests - Frontend to Backend', () => {
  describe('Complete Wealth Analysis Workflow', () => {
    it('should perform complete end-to-end workflow', async () => {
      // 1. Clear and setup initial data
      const setupResponse = await request(app)
        .post('/api/data/clear-and-setup')
        .send({
          portfolioValue: 800000,
          monthlyContribution: 6250
        });
      
      expect(setupResponse.status).toBe(200);
      expect(setupResponse.body.success).toBe(true);

      // 2. Add additional portfolio data
      const portfolioResponse = await request(app)
        .post('/api/portfolio')
        .send({
          year: 2024,
          month: 8,
          trow: 420000,
          robinhood: 210000,
          etrade: 160000,
          teradata: 55000,
          fidelity: 105000
        });
      
      expect(portfolioResponse.status).toBe(201);

      // 3. Add payslip data
      const payslipResponse = await request(app)
        .post('/api/payslips')
        .send({
          date: '2024-08-15',
          gross: 8500,
          net: 6200,
          espp: 850,
          rothE: 450,
          rothR: 450
        });
      
      expect(payslipResponse.status).toBe(201);

      // 4. Get basic analysis
      const analysisResponse = await request(app)
        .get('/api/analysis/basic');
      
      expect(analysisResponse.status).toBe(200);
      expect(analysisResponse.body.success).toBe(true);
      expect(analysisResponse.body.data).toHaveProperty('monthlyContribution');
      expect(analysisResponse.body.data).toHaveProperty('irr');
      expect(analysisResponse.body.data).toHaveProperty('currentPortfolioValue');

      // 5. Generate projections
      const projectionsResponse = await request(app)
        .get('/api/analysis/projections?target=1500000&contributions=5000,6000,6250');
      
      expect(projectionsResponse.status).toBe(200);
      expect(projectionsResponse.body.success).toBe(true);
      expect(projectionsResponse.body.data).toHaveProperty('projections');
      expect(projectionsResponse.body.data.projections).toHaveLength(4); // 4 scenarios

      // 6. Get milestone projections
      const milestoneResponse = await request(app)
        .get('/api/analysis/milestone-projections?target=1500000&contributions=4200,5260,6250');
      
      expect(milestoneResponse.status).toBe(200);
      expect(milestoneResponse.body.success).toBe(true);
      expect(milestoneResponse.body.data).toHaveProperty('projections');
      expect(milestoneResponse.body.data).toHaveProperty('currentValue');

      // 7. Get required contributions analysis
      const requiredResponse = await request(app)
        .get('/api/analysis/required-contributions');
      
      expect(requiredResponse.status).toBe(200);
      expect(requiredResponse.body.success).toBe(true);
      expect(requiredResponse.body.data).toHaveProperty('targets');

      // 8. Get component split analysis
      const componentResponse = await request(app)
        .get('/api/analysis/component-split');
      
      expect(componentResponse.status).toBe(200);
      expect(componentResponse.body.success).toBe(true);
      expect(componentResponse.body.data).toHaveProperty('projections');

      // 9. Get coast-fire analysis
      const coastFireResponse = await request(app)
        .get('/api/analysis/coast-fire');
      
      expect(coastFireResponse.status).toBe(200);
      expect(coastFireResponse.body.success).toBe(true);
      expect(coastFireResponse.body.data).toHaveProperty('thresholds');

      // 10. Save data
      const saveResponse = await request(app)
        .post('/api/data/save')
        .send({ filename: 'integration_test_backup.json' });
      
      expect(saveResponse.status).toBe(200);
      expect(saveResponse.body.success).toBe(true);
    });

    it('should handle invalid data gracefully throughout workflow', async () => {
      // 1. Clear data first to ensure no initial data
      await request(app).post('/api/data/reset');
      
      // 2. Try to analyze without sufficient data
      const analysisResponse = await request(app)
        .get('/api/analysis/basic');
      
      expect(analysisResponse.status).toBe(400);
      expect(analysisResponse.body.success).toBe(false);
      expect(analysisResponse.body.error).toContain('Insufficient data');

      // 3. Try invalid portfolio data
      const invalidPortfolioResponse = await request(app)
        .post('/api/portfolio')
        .send({
          year: 2024,
          month: 13, // Invalid month
          trow: 'invalid', // Invalid type
        });
      
      expect(invalidPortfolioResponse.status).toBe(400);

      // 4. Try invalid payslip data
      const invalidPayslipResponse = await request(app)
        .post('/api/payslips')
        .send({
          date: '2024-08-15',
          // Missing required fields
        });
      
      expect(invalidPayslipResponse.status).toBe(400);

      // 5. Try invalid config updates
      const invalidConfigResponse = await request(app)
        .put('/api/config/targets')
        .send({ targets: 'not-an-array' });
      
      expect(invalidConfigResponse.status).toBe(400);
    });
  });

  describe('Data Persistence and Recovery', () => {
    it('should persist and recover data correctly', async () => {
      // 1. Setup initial data
      await request(app)
        .post('/api/data/clear-and-setup')
        .send({
          portfolioValue: 500000,
          monthlyContribution: 5000
        });

      // 2. Add some data
      await request(app)
        .post('/api/portfolio')
        .send({
          year: 2024,
          month: 7,
          trow: 300000,
          robinhood: 150000,
          etrade: 100000,
          teradata: 50000
        });

      // 3. Get initial portfolio
      const initialResponse = await request(app)
        .get('/api/portfolio');
      
      expect(initialResponse.status).toBe(200);
      const initialData = initialResponse.body.data;

      // 4. Save data
      await request(app)
        .post('/api/data/save')
        .send({ filename: 'test_persistence.json' });

      // 5. Reset data
      await request(app)
        .post('/api/data/reset');

      // 6. Verify data is cleared
      const clearedResponse = await request(app)
        .get('/api/portfolio');
      
      expect(clearedResponse.status).toBe(200);
      expect(clearedResponse.body.data).toHaveLength(0);

      // 7. Load saved data
      await request(app)
        .post('/api/data/load')
        .send({ filename: 'test_persistence.json' });

      // 8. Verify data is restored
      const restoredResponse = await request(app)
        .get('/api/portfolio');
      
      expect(restoredResponse.status).toBe(200);
      expect(restoredResponse.body.data).toEqual(initialData);
    });
  });

  describe('Configuration Management', () => {
    it('should manage configuration through complete lifecycle', async () => {
      // 1. Get initial config
      const initialConfigResponse = await request(app)
        .get('/api/config');
      
      expect(initialConfigResponse.status).toBe(200);
      const initialConfig = initialConfigResponse.body.data;

      // 2. Update wealth targets
      const newTargets = [1000000, 2000000, 5000000];
      await request(app)
        .put('/api/config/targets')
        .send({ targets: newTargets });

      // 3. Update monthly contributions
      const newContributions = [4000, 5000, 6000, 7000];
      await request(app)
        .put('/api/config/contributions')
        .send({ contributions: newContributions });

      // 4. Update target amount
      await request(app)
        .put('/api/config/target')
        .send({ target: 2500000 });

      // 5. Update general config
      const configUpdate = {
        monthlyExpenses: 3200,
        paychecksPerYear: 24
      };
      await request(app)
        .put('/api/config')
        .send(configUpdate);

      // 6. Verify all changes
      const updatedConfigResponse = await request(app)
        .get('/api/config');
      
      expect(updatedConfigResponse.status).toBe(200);
      const updatedConfig = updatedConfigResponse.body.data;
      
      expect(updatedConfig.wealthTargets).toEqual(newTargets);
      expect(updatedConfig.defaultMonthlyContributions).toEqual(newContributions);
      expect(updatedConfig.defaultTarget).toBe(2500000);
      expect(updatedConfig.monthlyExpenses).toBe(3200);
      expect(updatedConfig.paychecksPerYear).toBe(24);

      // 7. Verify unchanged values remain
      expect(updatedConfig.annualBonusGross).toBe(initialConfig.annualBonusGross);
      expect(updatedConfig.bonusMonth).toBe(initialConfig.bonusMonth);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle network timeouts and errors gracefully', async () => {
      // Test various error conditions
      
      // 1. Invalid route
      const invalidRouteResponse = await request(app)
        .get('/api/nonexistent-endpoint');
      
      expect(invalidRouteResponse.status).toBe(404);
      expect(invalidRouteResponse.body.success).toBe(false);

      // 2. Invalid HTTP method
      const invalidMethodResponse = await request(app)
        .delete('/api/config');
      
      expect(invalidMethodResponse.status).toBe(404);

      // 3. Missing required fields
      const missingFieldsResponse = await request(app)
        .post('/api/data/clear-and-setup')
        .send({ portfolioValue: 100000 }); // Missing monthlyContribution
      
      expect(missingFieldsResponse.status).toBe(400);
      expect(missingFieldsResponse.body.error).toContain('Missing required fields');

      // 4. Invalid data types
      const invalidTypesResponse = await request(app)
        .post('/api/portfolio')
        .send({
          year: 'not-a-number',
          month: 7,
          trow: 100000,
          robinhood: 50000,
          etrade: 30000,
          teradata: 20000
        });
      
      expect(invalidTypesResponse.status).toBe(400);
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle multiple rapid requests', async () => {
      // Add delay to avoid rate limit issues from previous tests
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Setup initial data
      await request(app)
        .post('/api/data/clear-and-setup')
        .send({
          portfolioValue: 800000,
          monthlyContribution: 6000
        });

      // Add portfolio data
      await request(app)
        .post('/api/portfolio')
        .send({
          year: 2024,
          month: 7,
          trow: 400000,
          robinhood: 200000,
          etrade: 150000,
          teradata: 50000
        });

      // Add payslip data
      await request(app)
        .post('/api/payslips')
        .send({
          date: '2024-07-15',
          gross: 8000,
          net: 6000,
          espp: 800,
          rothE: 400,
          rothR: 400
        });

      // Make multiple rapid requests
      const requests = [
        request(app).get('/api/analysis/basic'),
        request(app).get('/api/analysis/projections'),
        request(app).get('/api/analysis/milestone-projections'),
        request(app).get('/api/analysis/required-contributions'),
        request(app).get('/api/analysis/component-split'),
        request(app).get('/api/analysis/coast-fire'),
        request(app).get('/api/portfolio'),
        request(app).get('/api/payslips'),
        request(app).get('/api/config'),
        request(app).get('/health')
      ];

      const responses = await Promise.all(requests);

      // All requests should succeed
      responses.forEach((response, index) => {
        expect(response.status).toBeLessThan(400);
      });
    });

    it('should handle large datasets efficiently', async () => {
      // Add delay to avoid rate limit issues from previous tests
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Setup with large dataset
      await request(app)
        .post('/api/data/clear-and-setup')
        .send({
          portfolioValue: 1000000,
          monthlyContribution: 10000
        });

      // Add many portfolio entries
      for (let year = 2020; year <= 2024; year++) {
        for (let month = 1; month <= 12; month++) {
          if (year === 2024 && month > 7) break; // Don't go beyond current date
          
          await request(app)
            .post('/api/portfolio')
            .send({
              year,
              month,
              trow: 300000 + (year - 2020) * 50000 + month * 2000,
              robinhood: 150000 + (year - 2020) * 25000 + month * 1000,
              etrade: 100000 + (year - 2020) * 15000 + month * 500,
              teradata: 50000 + (year - 2020) * 10000 + month * 200
            });
        }
      }

      // Add many payslip entries
      for (let i = 0; i < 50; i++) {
        const date = new Date(2024, 0, 1);
        date.setDate(date.getDate() + i * 14); // Bi-weekly
        
        await request(app)
          .post('/api/payslips')
          .send({
            date: date.toISOString().split('T')[0],
            gross: 8000 + Math.random() * 1000,
            net: 6000 + Math.random() * 500,
            espp: 800 + Math.random() * 200,
            rothE: 400 + Math.random() * 100,
            rothR: 400 + Math.random() * 100
          });
      }

      // Perform analysis with large dataset
      const analysisResponse = await request(app)
        .get('/api/analysis/basic');
      
      expect(analysisResponse.status).toBe(200);
      expect(analysisResponse.body.success).toBe(true);

      // Check that response time is reasonable (should complete within timeout)
      // This is implicitly tested by Jest timeout settings
    });
  });

  describe('Data Validation and Business Logic', () => {
    it('should validate business rules across the entire system', async () => {
      // Add delay to avoid rate limit issues from previous tests
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 1. Setup valid initial state
      await request(app)
        .post('/api/data/clear-and-setup')
        .send({
          portfolioValue: 500000,
          monthlyContribution: 5000
        });

      // 2. Add portfolio data in chronological order
      await request(app)
        .post('/api/portfolio')
        .send({
          year: 2024,
          month: 1,
          trow: 250000,
          robinhood: 125000,
          etrade: 75000,
          teradata: 50000
        });

      await request(app)
        .post('/api/portfolio')
        .send({
          year: 2024,
          month: 6,
          trow: 300000,
          robinhood: 150000,
          etrade: 100000,
          teradata: 50000
        });

      // 3. Add corresponding payslip data
      await request(app)
        .post('/api/payslips')
        .send({
          date: '2024-06-15',
          gross: 8000,
          net: 6000,
          espp: 800,
          rothE: 400,
          rothR: 400
        });

      // 4. Verify that analysis produces sensible results
      const analysisResponse = await request(app)
        .get('/api/analysis/basic');
      
      expect(analysisResponse.status).toBe(200);
      const analysis = analysisResponse.body.data;
      
      // Business rule validations
      expect(analysis.currentPortfolioValue).toBeGreaterThan(0);
      expect(analysis.monthlyContribution).toBeGreaterThan(0);
      expect(analysis.irr).toBeGreaterThan(-1); // IRR should be reasonable
      expect(analysis.irr).toBeLessThan(2); // IRR shouldn't be impossibly high
      
      // 5. Verify projections are realistic
      const projectionsResponse = await request(app)
        .get('/api/analysis/projections?target=1000000');
      
      expect(projectionsResponse.status).toBe(200);
      const projections = projectionsResponse.body.data.projections;
      
      // All scenarios should have positive months to target
      projections.forEach((scenario: any) => {
        scenario.contributions.forEach((contrib: any) => {
          expect(contrib.monthsToTarget).toBeGreaterThan(0);
          expect(contrib.monthsToTarget).toBeLessThan(1200); // Less than 100 years
        });
      });

      // 6. Verify milestone projections match format requirements
      const milestoneResponse = await request(app)
        .get('/api/analysis/milestone-projections?target=1500000&contributions=4200,5260,6250');
      
      expect(milestoneResponse.status).toBe(200);
      const milestoneData = milestoneResponse.body.data;
      
      expect(milestoneData.projections).toHaveLength(4); // Should have 4 scenarios
      expect(milestoneData.projections[0].contributions).toHaveLength(3); // Should have 3 contribution levels
      
      // Verify delay calculations
      milestoneData.projections.forEach((scenario: any) => {
        const contribs = scenario.contributions;
        // Delays should be relative to highest contribution (6250)
        expect(contribs[2].delayVsBaseline).toBe('–'); // Highest contribution has no delay
      });
    });
  });
});