import request from 'supertest';
import app, { setWealthTracker } from '../api/server';
import { WealthTracker } from '../wealth-tracker';

// Mock the WealthTracker class
jest.mock('../wealth-tracker');

describe('API Server Comprehensive Tests', () => {
  let mockWealthTracker: jest.Mocked<WealthTracker>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockWealthTracker = new WealthTracker() as jest.Mocked<WealthTracker>;
    
    // Inject the mock instance into the server
    setWealthTracker(mockWealthTracker);
    
    // Setup default mocks
    mockWealthTracker.getConfig.mockReturnValue({
      wealthTargets: [900000, 1500000, 2000000, 3000000],
      defaultTarget: 1500000,
      defaultMonthlyContributions: [3000, 4000, 5000, 6250],
      fallbackMonthlyContribution: 4000,
      annualBonusGross: 44792.41,
      annualBonusNet: 26391.69,
      bonusMonth: 3,
      paychecksPerYear: 26,
      monthlyExpenses: 2800,
      analysisTimeframes: [12, 24, 36],
      coastFireRetirementAge: 65,
      coastFireWithdrawalRate: 0.04
    });
    
    mockWealthTracker.hasSufficientDataForAnalysis.mockReturnValue(true);
    mockWealthTracker.calculateAverageMonthlyContribution.mockReturnValue(6000);
    mockWealthTracker.calculateIrr.mockReturnValue(0.02);
    mockWealthTracker.calculateStatistics.mockReturnValue({
      meanReturn: 0.025,
      stdDev: 0.01
    });
    mockWealthTracker.getMostRecentPortfolioEntry.mockReturnValue({
      date: '2024-07',
      trow: 400000,
      robinhood: 200000,
      etrade: 150000,
      teradata: 50000,
      fidelity: 0
    });
    mockWealthTracker.projectFuture.mockReturnValue(24);
    mockWealthTracker.getPortfolioHistory.mockReturnValue([]);
    mockWealthTracker.getMostRecentPayslips.mockReturnValue([]);
  });

  describe('Health Check', () => {
    it('should return healthy status', async () => {
      const response = await request(app).get('/health');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'healthy');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('uptime');
    });
  });

  describe('Configuration Endpoints', () => {
    it('should get configuration', async () => {
      const response = await request(app).get('/api/config');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('wealthTargets');
    });

    it('should handle config get error', async () => {
      mockWealthTracker.getConfig.mockImplementation(() => {
        throw new Error('Config error');
      });

      const response = await request(app).get('/api/config');
      
      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
    });

    it('should update configuration', async () => {
      const newConfig = {
        defaultTarget: 2000000,
        monthlyExpenses: 3000
      };

      const response = await request(app)
        .put('/api/config')
        .send(newConfig);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(mockWealthTracker.updateConfig).toHaveBeenCalledWith(newConfig);
    });

    it('should handle config update error', async () => {
      mockWealthTracker.updateConfig.mockImplementation(() => {
        throw new Error('Update error');
      });

      const response = await request(app)
        .put('/api/config')
        .send({ defaultTarget: 2000000 });
      
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should update wealth targets', async () => {
      const targets = [1000000, 2000000, 3000000];

      const response = await request(app)
        .put('/api/config/targets')
        .send({ targets });
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(mockWealthTracker.setWealthTargets).toHaveBeenCalledWith(targets);
    });

    it('should reject invalid wealth targets', async () => {
      const response = await request(app)
        .put('/api/config/targets')
        .send({ targets: [1000000, -500000] });
      
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid targets');
    });

    it('should update contributions', async () => {
      const contributions = [4000, 5000, 6000];

      const response = await request(app)
        .put('/api/config/contributions')
        .send({ contributions });
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(mockWealthTracker.setDefaultMonthlyContributions).toHaveBeenCalledWith(contributions);
    });

    it('should reject invalid contributions', async () => {
      const response = await request(app)
        .put('/api/config/contributions')
        .send({ contributions: 'not-an-array' });
      
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should update target amount', async () => {
      const response = await request(app)
        .put('/api/config/target')
        .send({ target: 2500000 });
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(mockWealthTracker.setTargetAmount).toHaveBeenCalledWith(2500000);
    });

    it('should reject invalid target amount', async () => {
      const response = await request(app)
        .put('/api/config/target')
        .send({ target: -1000000 });
      
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Portfolio Endpoints', () => {
    it('should get portfolio history', async () => {
      const mockHistory = [
        { date: '2024-01', trow: 300000, robinhood: 150000, etrade: 100000, teradata: 50000 }
      ];
      mockWealthTracker.getPortfolioHistory.mockReturnValue(mockHistory);

      const response = await request(app).get('/api/portfolio');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockHistory);
    });

    it('should handle portfolio get error', async () => {
      mockWealthTracker.getPortfolioHistory.mockImplementation(() => {
        throw new Error('Portfolio error');
      });

      const response = await request(app).get('/api/portfolio');
      
      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
    });

    it('should add portfolio data', async () => {
      mockWealthTracker.addPortfolioDataIfComplete.mockReturnValue(true);

      const portfolioData = {
        year: 2024,
        month: 7,
        trow: 400000,
        robinhood: 200000,
        etrade: 150000,
        teradata: 50000,
        fidelity: 100000
      };

      const response = await request(app)
        .post('/api/portfolio')
        .send(portfolioData);
      
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
    });

    it('should reject incomplete portfolio data', async () => {
      const response = await request(app)
        .post('/api/portfolio')
        .send({ year: 2024, month: 7 });
      
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Missing required fields');
    });

    it('should handle failed portfolio addition', async () => {
      mockWealthTracker.addPortfolioDataIfComplete.mockReturnValue(false);

      const portfolioData = {
        year: 2024,
        month: 7,
        trow: 400000,
        robinhood: 200000,
        etrade: 150000,
        teradata: 50000
      };

      const response = await request(app)
        .post('/api/portfolio')
        .send(portfolioData);
      
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should get recent portfolio entry', async () => {
      const response = await request(app).get('/api/portfolio/recent');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('trow', 400000);
    });

    it('should handle recent portfolio error', async () => {
      mockWealthTracker.getMostRecentPortfolioEntry.mockImplementation(() => {
        throw new Error('Recent portfolio error');
      });

      const response = await request(app).get('/api/portfolio/recent');
      
      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Payslip Endpoints', () => {
    it('should get payslips with default count', async () => {
      const mockPayslips = [
        { date: '2024-07-15', gross: 8000, net: 6000, espp: 800, roth_e: 400, roth_r: 400, total_invest: 1600 }
      ];
      mockWealthTracker.getMostRecentPayslips.mockReturnValue(mockPayslips);

      const response = await request(app).get('/api/payslips');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockPayslips);
      expect(mockWealthTracker.getMostRecentPayslips).toHaveBeenCalledWith(10);
    });

    it('should get payslips with custom count', async () => {
      const response = await request(app).get('/api/payslips?count=5');
      
      expect(response.status).toBe(200);
      expect(mockWealthTracker.getMostRecentPayslips).toHaveBeenCalledWith(5);
    });

    it('should add payslip', async () => {
      const payslipData = {
        date: '2024-07-15',
        gross: 8000,
        net: 6000,
        espp: 800,
        rothE: 400,
        rothR: 400
      };

      const response = await request(app)
        .post('/api/payslips')
        .send(payslipData);
      
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(mockWealthTracker.addPayslip).toHaveBeenCalled();
    });

    it('should reject incomplete payslip data', async () => {
      const response = await request(app)
        .post('/api/payslips')
        .send({ date: '2024-07-15', gross: 8000 });
      
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Missing required fields');
    });

    it('should handle payslip add error', async () => {
      mockWealthTracker.addPayslip.mockImplementation(() => {
        throw new Error('Payslip error');
      });

      const payslipData = {
        date: '2024-07-15',
        gross: 8000,
        net: 6000,
        espp: 800,
        rothE: 400,
        rothR: 400
      };

      const response = await request(app)
        .post('/api/payslips')
        .send(payslipData);
      
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Analysis Endpoints', () => {
    it('should get basic analysis', async () => {
      const response = await request(app).get('/api/analysis/basic');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('monthlyContribution', 6000);
      expect(response.body.data).toHaveProperty('irr', 0.02);
      expect(response.body.data).toHaveProperty('currentPortfolioValue', 800000);
    });

    it('should reject analysis with insufficient data', async () => {
      mockWealthTracker.hasSufficientDataForAnalysis.mockReturnValue(false);

      const response = await request(app).get('/api/analysis/basic');
      
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Insufficient data');
    });

    it('should handle analysis error', async () => {
      mockWealthTracker.calculateIrr.mockImplementation(() => {
        throw new Error('Analysis error');
      });

      const response = await request(app).get('/api/analysis/basic');
      
      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
    });

    it('should get projections with default values', async () => {
      const response = await request(app).get('/api/analysis/projections');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('projections');
      expect(response.body.data.projections).toHaveLength(4); // 4 scenarios
    });

    it('should get projections with custom values', async () => {
      const response = await request(app)
        .get('/api/analysis/projections?target=2000000&contributions=5000,6000,7000');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.target).toBe(2000000);
    });

    it('should get required contributions analysis', async () => {
      mockWealthTracker.analyzeRequiredContributions.mockReturnValue({
        targets: [
          {
            amount: 1500000,
            timeframes: {
              '12months': { requiredPmt: 10000 },
              '24months': { requiredPmt: 6000 },
              '36months': { requiredPmt: 4000 }
            }
          }
        ]
      });

      const response = await request(app).get('/api/analysis/required-contributions');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('targets');
    });

    it('should get component split analysis', async () => {
      mockWealthTracker.analyzeComponentSplit.mockReturnValue({
        projections: [
          {
            scenario: 'Conservative',
            FV_total: 1000000,
            FV_from_PV: 800000,
            FV_from_contr: 200000,
            share_contr: 0.2
          }
        ]
      });

      const response = await request(app).get('/api/analysis/component-split');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('projections');
    });

    it('should get coast-fire analysis', async () => {
      mockWealthTracker.analyzeCoastFire.mockReturnValue({
        thresholds: {
          conservative: 500000,
          baseline: 400000,
          optimistic: 300000
        },
        currentStatus: 'Achieved',
        monthsToCoastFire: 0
      });

      const response = await request(app).get('/api/analysis/coast-fire');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('thresholds');
    });

    it('should get milestone projections', async () => {
      const response = await request(app)
        .get('/api/analysis/milestone-projections?target=1500000&contributions=4200,5260,6250');
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('projections');
      expect(response.body.data).toHaveProperty('currentValue', 800000);
    });

    it('should handle milestone projections without portfolio data', async () => {
      mockWealthTracker.getMostRecentPortfolioEntry.mockReturnValue(null);

      const response = await request(app).get('/api/analysis/milestone-projections');
      
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('No portfolio data available');
    });
  });

  describe('Data Management Endpoints', () => {
    it('should save data with default filename', async () => {
      const response = await request(app)
        .post('/api/data/save')
        .send({});
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(mockWealthTracker.saveData).toHaveBeenCalledWith('wealth_tracker_data.json');
    });

    it('should save data with custom filename', async () => {
      const response = await request(app)
        .post('/api/data/save')
        .send({ filename: 'custom_backup.json' });
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(mockWealthTracker.saveData).toHaveBeenCalledWith('custom_backup.json');
    });

    it('should handle save error', async () => {
      mockWealthTracker.saveData.mockImplementation(() => {
        throw new Error('Save error');
      });

      const response = await request(app)
        .post('/api/data/save')
        .send({});
      
      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
    });

    it('should load data', async () => {
      const response = await request(app)
        .post('/api/data/load')
        .send({ filename: 'backup.json' });
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(mockWealthTracker.loadData).toHaveBeenCalledWith('backup.json');
    });

    it('should handle load error', async () => {
      mockWealthTracker.loadData.mockImplementation(() => {
        throw new Error('Load error');
      });

      const response = await request(app)
        .post('/api/data/load')
        .send({});
      
      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
    });

    it('should reset data', async () => {
      const response = await request(app)
        .post('/api/data/reset')
        .send({});
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should clear and setup data', async () => {
      const response = await request(app)
        .post('/api/data/clear-and-setup')
        .send({
          portfolioValue: 800000,
          monthlyContribution: 6250
        });
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('portfolioValue', 800000);
      expect(response.body.data).toHaveProperty('monthlyContribution', 6250);
    });

    it('should reject clear-and-setup with missing fields', async () => {
      const response = await request(app)
        .post('/api/data/clear-and-setup')
        .send({ portfolioValue: 800000 });
      
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Missing required fields');
    });

    it('should reject clear-and-setup with negative values', async () => {
      const response = await request(app)
        .post('/api/data/clear-and-setup')
        .send({
          portfolioValue: -100000,
          monthlyContribution: 5000
        });
      
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('must be non-negative');
    });
  });

  describe('Error Handling', () => {
    it('should handle 404 for unknown routes', async () => {
      const response = await request(app).get('/api/unknown-endpoint');
      
      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Endpoint not found');
    });

    it('should handle 404 for non-API routes', async () => {
      const response = await request(app).get('/some-random-path');
      
      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Rate Limiting', () => {
    it('should not apply rate limiting in test environment', async () => {
      // Make many requests - should all succeed in test environment
      const requests = Array(10).fill(null).map(() => 
        request(app).get('/api/config')
      );

      const responses = await Promise.all(requests);
      
      // All requests should succeed (no 429 status)
      const allSuccessful = responses.every(r => r.status === 200);
      expect(allSuccessful).toBe(true);
    });
  });
});