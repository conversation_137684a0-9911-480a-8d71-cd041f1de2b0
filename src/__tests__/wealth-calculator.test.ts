import { WealthCalculator } from '../services/wealth-calculator';
import * as formulas from '../formulas';
import { PortfolioEntry, PayslipEntry } from '../types';

describe('WealthCalculator Service Tests', () => {
  let calculator: WealthCalculator;
  let mockPortfolioData: PortfolioEntry[];
  let mockPayslipData: PayslipEntry[];

  beforeEach(() => {
    mockPortfolioData = [
      { date: '2023-12', trow: 160000, robinhood: 60000, etrade: 155000, teradata: 32000 },
      { date: '2024-07', trow: 270000, robinhood: 115000, etrade: 366000, teradata: 35000 }
    ];
    
    mockPayslipData = [
      { date: '2024-01-15', gross: 6419.23, net: 1344.56, espp: 962.88, roth_e: 1669.00, roth_r: 256.77, total_invest: 2888.65 },
      { date: '2024-02-15', gross: 6419.23, net: 1305.93, espp: 962.88, roth_e: 1669.00, roth_r: 256.77, total_invest: 2888.65 }
    ];

    calculator = new WealthCalculator(mockPortfolioData, mockPayslipData);
  });

  describe('Service Initialization', () => {
    it('should initialize with portfolio and payslip data', () => {
      expect(calculator).toBeDefined();
      expect(calculator.getPortfolioData()).toEqual(mockPortfolioData);
      expect(calculator.getPayslipData()).toEqual(mockPayslipData);
    });

    it('should calculate initial metrics on construction', () => {
      const metrics = calculator.getCurrentMetrics();
      
      expect(metrics).toHaveProperty('currentValue');
      expect(metrics).toHaveProperty('monthlyContribution');
      expect(metrics).toHaveProperty('irr');
      expect(metrics).toHaveProperty('statistics');
      expect(metrics.currentValue).toBeGreaterThan(0);
    });
  });

  describe('Comprehensive Analysis', () => {
    it('should provide complete financial analysis using all formulas', () => {
      const analysis = calculator.calculateComprehensiveAnalysis();
      
      // Current status
      expect(analysis.currentStatus).toHaveProperty('totalValue');
      expect(analysis.currentStatus).toHaveProperty('breakdown');
      expect(analysis.currentStatus).toHaveProperty('date');
      
      // Performance metrics
      expect(analysis.performance).toHaveProperty('irr');
      expect(analysis.performance).toHaveProperty('annualizedReturn');
      expect(analysis.performance).toHaveProperty('statistics');
      expect(analysis.performance).toHaveProperty('scenarioRates');
      
      // Projections
      expect(analysis.projections).toHaveProperty('targets');
      expect(analysis.projections.targets).toHaveLength(3);
      
      // Optimization
      expect(analysis.optimization).toHaveProperty('requiredContributions');
      expect(analysis.optimization).toHaveProperty('componentSplits');
      
      // Coast-FIRE
      expect(analysis.coastFire).toHaveProperty('thresholds');
      expect(analysis.coastFire).toHaveProperty('status');
    });

    it('should use formulas.calculateScenarioRates for scenario analysis', () => {
      const analysis = calculator.calculateComprehensiveAnalysis();
      const metrics = calculator.getCurrentMetrics();
      
      const expectedScenarios = formulas.calculateScenarioRates(
        metrics.irr,
        metrics.statistics.mean,
        metrics.statistics.stdDev
      );
      
      expect(analysis.performance.scenarioRates.conservative).toBeCloseTo(expectedScenarios.conservative, 6);
      expect(analysis.performance.scenarioRates.baseline).toBeCloseTo(expectedScenarios.baseline, 6);
      expect(analysis.performance.scenarioRates.optimistic).toBeCloseTo(expectedScenarios.optimistic, 6);
    });
  });

  describe('Scenario Projections', () => {
    it('should project multiple targets across all scenarios', () => {
      const targets = [1000000, 1500000, 2000000];
      const scenarios = calculator.projectScenarios(targets);
      
      expect(scenarios).toHaveLength(3);
      
      scenarios.forEach((scenario, index) => {
        expect(scenario.target).toBe(targets[index]);
        expect(scenario).toHaveProperty('conservative');
        expect(scenario).toHaveProperty('baseline');
        expect(scenario).toHaveProperty('optimistic');
        
        ['conservative', 'baseline', 'optimistic'].forEach(rateType => {
          const projection = scenario[rateType as 'conservative' | 'baseline' | 'optimistic'];
          expect(projection).toHaveProperty('months');
          expect(projection).toHaveProperty('date');
          expect(projection).toHaveProperty('futureValue');
          expect(projection).toHaveProperty('componentSplit');

          expect(projection.months).toBeGreaterThan(0);
          expect(projection.futureValue).toBeGreaterThanOrEqual(targets[index]);
        });
      });
    });

    it('should use formulas.monthsToTarget for time calculations', () => {
      const targets = [1000000];
      const scenarios = calculator.projectScenarios(targets);
      const metrics = calculator.getCurrentMetrics();
      
      const expectedMonths = formulas.monthsToTarget(
        metrics.currentValue,
        1000000,
        metrics.irr,
        metrics.monthlyContribution
      );
      
      expect(scenarios[0].baseline.months).toBe(expectedMonths);
    });

    it('should use formulas.componentSplit for breakdown analysis', () => {
      const targets = [1000000];
      const scenarios = calculator.projectScenarios(targets);
      const metrics = calculator.getCurrentMetrics();
      
      const months = scenarios[0].baseline.months;
      const expectedSplit = formulas.componentSplit(
        metrics.currentValue,
        metrics.irr,
        metrics.monthlyContribution,
        months
      );
      
      const actualSplit = scenarios[0].baseline.componentSplit;
      expect(actualSplit.FV_total).toBe(expectedSplit.FV_total);
      expect(actualSplit.FV_from_PV).toBe(expectedSplit.FV_from_PV);
      expect(actualSplit.FV_from_contr).toBe(expectedSplit.FV_from_contr);
      expect(actualSplit.share_contr).toBe(expectedSplit.share_contr);
    });
  });

  describe('Contribution Optimization', () => {
    it('should calculate required contributions for different timeframes', () => {
      const optimization = calculator.optimizeContributions();
      
      expect(optimization).toHaveProperty('currentContribution');
      expect(optimization).toHaveProperty('recommendations');
      expect(optimization.recommendations).toHaveLength(9); // 3 targets × 3 timeframes
      
      optimization.recommendations.forEach(rec => {
        expect(rec).toHaveProperty('target');
        expect(rec).toHaveProperty('timeframe');
        expect(rec).toHaveProperty('requiredContribution');
        expect(rec).toHaveProperty('additionalNeeded');
        expect(rec).toHaveProperty('feasibility');

        expect([1000000, 1500000, 2000000]).toContain(rec.target);
        expect([12, 24, 36]).toContain(rec.timeframe);
        // Required contribution can be negative if current portfolio is already sufficient
        expect(typeof rec.requiredContribution).toBe('number');
        expect(rec.requiredContribution).not.toBeNaN();
      });
    });

    it('should use formulas.requiredContributionForTarget for calculations', () => {
      const optimization = calculator.optimizeContributions();
      const metrics = calculator.getCurrentMetrics();
      
      const target = 1000000;
      const timeframe = 24;
      const expectedPmt = formulas.requiredContributionForTarget(
        metrics.currentValue,
        target,
        metrics.irr,
        timeframe
      );
      
      const recommendation = optimization.recommendations.find(
        r => r.target === target && r.timeframe === timeframe
      );

      expect(recommendation).toBeDefined();
      expect(recommendation!.requiredContribution).toBeCloseTo(expectedPmt, 2);
    });

    it('should assess feasibility based on current contribution capacity', () => {
      const optimization = calculator.optimizeContributions();
      const currentContribution = optimization.currentContribution;
      
      optimization.recommendations.forEach(rec => {
        if (rec.requiredContribution <= currentContribution * 1.2) {
          expect(rec.feasibility).toBe('High');
        } else if (rec.requiredContribution <= currentContribution * 1.5) {
          expect(rec.feasibility).toBe('Medium');
        } else {
          expect(rec.feasibility).toBe('Low');
        }
      });
    });
  });

  describe('Coast-FIRE Analysis', () => {
    it('should calculate coast-FIRE thresholds for all scenarios', () => {
      const coastFire = calculator.analyzeCoastFire();
      
      expect(coastFire).toHaveProperty('thresholds');
      expect(coastFire.thresholds).toHaveProperty('conservative');
      expect(coastFire.thresholds).toHaveProperty('baseline');
      expect(coastFire.thresholds).toHaveProperty('optimistic');
      
      expect(coastFire).toHaveProperty('status');
      expect(coastFire).toHaveProperty('monthsToCoastFire');
      expect(coastFire).toHaveProperty('currentProgress');
    });

    it('should use formulas.coastFireThreshold for calculations', () => {
      const coastFire = calculator.analyzeCoastFire();
      const metrics = calculator.getCurrentMetrics();
      
      const expectedThreshold = formulas.coastFireThreshold(
        metrics.irr,
        metrics.monthlyContribution
      );
      
      expect(coastFire.thresholds.baseline).toBe(expectedThreshold);
    });

    it('should determine coast-FIRE status correctly', () => {
      const coastFire = calculator.analyzeCoastFire();
      const metrics = calculator.getCurrentMetrics();
      
      const isAtCoastFire = metrics.currentValue >= coastFire.thresholds.baseline;
      
      if (isAtCoastFire) {
        expect(coastFire.status).toBe('Achieved');
        expect(coastFire.monthsToCoastFire).toBe(0);
        expect(coastFire.currentProgress).toBeGreaterThanOrEqual(1);
      } else {
        expect(coastFire.status).toBe('Not Achieved');
        expect(coastFire.monthsToCoastFire).toBeGreaterThan(0);
        expect(coastFire.currentProgress).toBeLessThan(1);
      }
    });
  });

  describe('Caching and Performance', () => {
    it('should cache expensive calculations', () => {
      // First call
      const start1 = performance.now();
      const analysis1 = calculator.calculateComprehensiveAnalysis();
      const time1 = performance.now() - start1;
      
      // Second call (should be faster due to caching)
      const start2 = performance.now();
      const analysis2 = calculator.calculateComprehensiveAnalysis();
      const time2 = performance.now() - start2;
      
      console.log(`DEBUG cache performance: First call ${time1}ms, Second call ${time2}ms`);
      
      expect(analysis1).toEqual(analysis2);
      // For very fast operations, caching may not show measurable difference
      // Just verify both calls return the same result and second is not significantly slower
      expect(time2).toBeLessThanOrEqual(time1 * 2); // Allow some variance
    });

    it('should invalidate cache when data changes', () => {
      const analysis1 = calculator.calculateComprehensiveAnalysis();
      
      // Update data
      const newPortfolioData = [...mockPortfolioData, 
        { date: '2024-12', trow: 300000, robinhood: 120000, etrade: 400000, teradata: 40000 }
      ];
      calculator.updatePortfolioData(newPortfolioData);
      
      const analysis2 = calculator.calculateComprehensiveAnalysis();
      
      expect(analysis1.currentStatus.totalValue).not.toBe(analysis2.currentStatus.totalValue);
    });
  });
});
