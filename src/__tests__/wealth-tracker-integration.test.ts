import { WealthTracker } from '../wealth-tracker';
import * as formulas from '../formulas';
import { expectedTestData } from './data/expected';

describe('WealthTracker Formula Integration Tests', () => {
  let tracker: WealthTracker;

  beforeEach(() => {
    tracker = new WealthTracker();
    // Add some test data
    tracker.addPortfolioData(2024, 1, 200000, 75000, 250000, 32000);
    tracker.addPortfolioData(2024, 7, 270000, 115000, 366000, 35000);
  });

  describe('Expected Data Validation Tests', () => {
    let testTracker: WealthTracker;

    beforeEach(() => {
      testTracker = new WealthTracker();
      // Set up tracker with expected test data baseline
      const { meta } = expectedTestData;
      // Clear initial data and set up with test baseline
      (testTracker as any).portfolioHistory = [{
        date: '2025-07',
        trow: meta.start_balance * 0.35,  // Approximate distribution
        robinhood: meta.start_balance * 0.15,
        etrade: meta.start_balance * 0.35,
        teradata: meta.start_balance * 0.15,
        fidelity: 0
      }];
    });

    describe('Formula Integration with Expected Data', () => {
      it('should match expected D-1 future value calculations', () => {
        const testCases = expectedTestData.formula_tests.filter(test => test.formula === 'D-1');

        testCases.forEach(testCase => {
          const { PV, r, PMT, n } = testCase.inputs;
          const result = formulas.futureValueWithContributions(PV!, r!, PMT!, n!);

          expect(Math.round(result)).toBeCloseTo(testCase.expect_FV!, -2); // Within $100
        });
      });

      it('should match expected D-3 months-to-target calculations', () => {
        const testCases = expectedTestData.formula_tests.filter(test => test.formula === 'D-3');

        testCases.forEach(testCase => {
          const { PV, FV, r, PMT } = testCase.inputs;
          const result = formulas.monthsToTarget(PV!, FV!, r!, PMT!);

          expect(result).toBe(testCase.expect_n!);
        });
      });

      it('should match expected D-4 delay calculations', () => {
        const testCases = expectedTestData.formula_tests.filter(test => test.formula === 'D-4');

        testCases.forEach(testCase => {
          const { PV, FV, r, PMT_fast, PMT_slow } = testCase.inputs;
          const result = formulas.deltaMonthsBetweenContributions(PV!, FV!, r!, PMT_fast!, PMT_slow!);

          expect(result).toBe(testCase.expect_Δn!);
        });
      });

      it('should match expected F-6 required contribution calculations', () => {
        const testCases = expectedTestData.formula_tests.filter(test => test.formula === 'F-6');

        testCases.forEach(testCase => {
          const { PV, FV, r, n } = testCase.inputs;
          const result = formulas.requiredContributionForTarget(PV!, FV!, r!, n!);

          expect(result).toBeCloseTo(testCase.expect_PMT!, 2);
        });
      });

      it('should match expected F-8 component split calculations', () => {
        const testCases = expectedTestData.formula_tests.filter(test => test.formula === 'F-8');

        testCases.forEach(testCase => {
          const { PV, r, PMT, n } = testCase.inputs;
          const result = formulas.componentSplit(PV!, r!, PMT!, n!);

          expect(result.FV_total).toBe(testCase.expect!.FV_total);
          expect(result.FV_from_PV).toBe(testCase.expect!.FV_from_PV);
          expect(result.FV_from_contr).toBe(testCase.expect!.FV_from_contr);
          expect(result.share_contr).toBeCloseTo(testCase.expect!.share_contr, 3);
        });
      });

      it('should match expected F-9 coast-fire threshold calculations', () => {
        const testCases = expectedTestData.formula_tests.filter(test => test.formula === 'F-9');

        testCases.forEach(testCase => {
          const { monthly_r, PMT } = testCase.inputs;
          const result = formulas.coastFireThreshold(monthly_r!, PMT!);

          expect(result).toBe(testCase.expect_PV_coast!);
        });
      });
    });
  });

  describe('Immediate Integration: Replace Duplicate Methods', () => {
    describe('IRR Calculation Integration', () => {
      it('should use formulas.calculatePortfolioIRR instead of custom calculateIrr', () => {
        // This test will fail initially because WealthTracker uses custom IRR
        const trackerIrr = tracker.calculateIrr();
        
        // Get portfolio values for formula comparison
        const portfolioValues = tracker.getPortfolioHistory().map(entry => 
          entry.trow + entry.robinhood + entry.etrade + entry.teradata + (entry.fidelity || 0)
        );
        const monthlyContribution = tracker.calculateAverageMonthlyContribution();
        
        const formulaIrr = formulas.calculatePortfolioIRR(portfolioValues, monthlyContribution);
        
        // Should be the same when using the same formula
        expect(trackerIrr).toBeCloseTo(formulaIrr, 4);
      });

      it('should handle edge cases consistently with formulas', () => {
        // Test with actual portfolio data - should return reasonable IRR values
        const trackerIrr = tracker.calculateIrr();

        // IRR should be a reasonable monthly rate (between -100% and +100%)
        expect(trackerIrr).toBeGreaterThanOrEqual(-1);
        expect(trackerIrr).toBeLessThan(1); // Reasonable upper bound for monthly returns
        expect(trackerIrr).not.toBeNaN();
        expect(isFinite(trackerIrr)).toBe(true);
      });
    });

    describe('Future Projection Integration', () => {
      it('should use formulas.monthsToTarget instead of custom projectFuture', () => {
        const target = 1000000;
        const monthlyRate = 0.02;
        const monthlyContribution = 6250;
        
        const trackerMonths = tracker.projectFuture(target, monthlyRate, monthlyContribution);
        
        // Get current portfolio value
        const currentValue = tracker.getPortfolioHistory()
          .slice(-1)[0];
        const pv = currentValue.trow + currentValue.robinhood + currentValue.etrade + 
                  currentValue.teradata + (currentValue.fidelity || 0);
        
        const formulaMonths = formulas.monthsToTarget(pv, target, monthlyRate, monthlyContribution);
        
        expect(trackerMonths).toBe(formulaMonths);
      });

      it('should handle zero rate scenarios consistently', () => {
        const target = 1000000;
        const monthlyRate = 0;
        const monthlyContribution = 6250;
        
        const trackerMonths = tracker.projectFuture(target, monthlyRate, monthlyContribution);
        
        const currentValue = tracker.getPortfolioHistory().slice(-1)[0];
        const pv = currentValue.trow + currentValue.robinhood + currentValue.etrade + 
                  currentValue.teradata + (currentValue.fidelity || 0);
        
        const formulaMonths = formulas.monthsToTarget(pv, target, monthlyRate, monthlyContribution);
        
        expect(trackerMonths).toBe(formulaMonths);
      });
    });

    describe('Statistics Integration', () => {
      it('should use formulas.calculateStatistics instead of custom implementation', () => {
        const trackerStats = tracker.calculateStatistics();
        
        // Get period returns for formula comparison
        const returns = tracker.calculatePeriodReturns();
        const formulaStats = formulas.calculateStatistics(returns);
        
        expect(trackerStats.meanReturn).toBeCloseTo(formulaStats.mean, 6);
        expect(trackerStats.stdDev).toBeCloseTo(formulaStats.stdDev, 6);
      });
    });
  });

  describe('Short-term Integration: Add Missing Formula Usage', () => {
    describe('Required Contribution Analysis (F-6)', () => {
      it('should calculate required contribution for specific targets and timeframes', () => {
        // This will fail initially as this functionality doesn't exist
        const analysis = tracker.analyzeRequiredContributions();
        
        expect(analysis).toHaveProperty('targets');
        expect(analysis.targets).toHaveLength(4); // 900K, 1.5M, 2M, 3M
        
        analysis.targets.forEach(target => {
          expect(target).toHaveProperty('amount');
          expect(target).toHaveProperty('timeframes');
          expect(target.timeframes).toHaveProperty('12months');
          expect(target.timeframes).toHaveProperty('24months');
          expect(target.timeframes).toHaveProperty('36months');
        });
      });

      it('should use formulas.requiredContributionForTarget for calculations', () => {
        // Get actual current value and IRR from tracker
        const currentValue = tracker.getPortfolioHistory().slice(-1)[0];
        const pv = currentValue.trow + currentValue.robinhood + currentValue.etrade +
                  currentValue.teradata + (currentValue.fidelity || 0);
        const target = 900000; // First target in default config
        const rate = tracker.calculateIrr();
        const months = 12;

        const expectedPmt = formulas.requiredContributionForTarget(pv, target, rate, months);

        const analysis = tracker.analyzeRequiredContributions();
        const targetAnalysis = analysis.targets.find(t => t.amount === 900000);

        expect(targetAnalysis).toBeDefined();
        expect(targetAnalysis!.timeframes['12months'].requiredPmt).toBeCloseTo(expectedPmt, 2);
      });
    });

    describe('Component Split Analysis (F-8)', () => {
      it('should provide detailed breakdown of future value components', () => {
        // This will fail initially as this functionality doesn't exist
        const analysis = tracker.analyzeComponentSplit();
        
        expect(analysis).toHaveProperty('projections');
        expect(analysis.projections).toHaveLength(3); // Conservative, Baseline, Optimistic
        
        analysis.projections.forEach(projection => {
          expect(projection).toHaveProperty('scenario');
          expect(projection).toHaveProperty('FV_total');
          expect(projection).toHaveProperty('FV_from_PV');
          expect(projection).toHaveProperty('FV_from_contr');
          expect(projection).toHaveProperty('share_contr');
        });
      });

      it('should use formulas.componentSplit for calculations', () => {
        // Get actual values from tracker
        const currentValue = tracker.getPortfolioHistory().slice(-1)[0];
        const pv = currentValue.trow + currentValue.robinhood + currentValue.etrade +
                  currentValue.teradata + (currentValue.fidelity || 0);
        const r = tracker.calculateIrr();
        const pmt = tracker.calculateAverageMonthlyContribution();
        const n = 24;

        // Calculate scenario rates to get baseline
        const {meanReturn, stdDev} = tracker.calculateStatistics();
        const scenarios = formulas.calculateScenarioRates(r, meanReturn, stdDev);
        const expectedSplit = formulas.componentSplit(pv, scenarios.baseline, pmt, n);

        const analysis = tracker.analyzeComponentSplit();
        const baselineProjection = analysis.projections.find(p => p.scenario === 'Baseline');

        expect(baselineProjection).toBeDefined();
        expect(baselineProjection!.FV_total).toBe(expectedSplit.FV_total);
        expect(baselineProjection!.FV_from_PV).toBe(expectedSplit.FV_from_PV);
        expect(baselineProjection!.FV_from_contr).toBe(expectedSplit.FV_from_contr);
        expect(baselineProjection!.share_contr).toBe(expectedSplit.share_contr);
      });
    });

    describe('Coast-FIRE Analysis (F-9)', () => {
      it('should calculate coast-FIRE threshold for different scenarios', () => {
        // This will fail initially as this functionality doesn't exist
        const analysis = tracker.analyzeCoastFire();
        
        expect(analysis).toHaveProperty('thresholds');
        expect(analysis.thresholds).toHaveProperty('conservative');
        expect(analysis.thresholds).toHaveProperty('baseline');
        expect(analysis.thresholds).toHaveProperty('optimistic');
        expect(analysis).toHaveProperty('currentStatus');
        expect(analysis).toHaveProperty('monthsToCoastFire');
      });

      it('should use formulas.coastFireThreshold for calculations', () => {
        // Get actual values from tracker
        const monthlyContribution = tracker.calculateAverageMonthlyContribution();
        const irr = tracker.calculateIrr();
        const {meanReturn, stdDev} = tracker.calculateStatistics();
        const scenarios = formulas.calculateScenarioRates(irr, meanReturn, stdDev);

        const expectedThreshold = formulas.coastFireThreshold(scenarios.baseline, monthlyContribution);

        const analysis = tracker.analyzeCoastFire();

        expect(analysis.thresholds.baseline).toBe(expectedThreshold);
      });

      it('should determine if already at coast-FIRE', () => {
        const analysis = tracker.analyzeCoastFire();
        
        expect(analysis.currentStatus).toMatch(/^(Achieved|Not Achieved)$/);
        
        if (analysis.currentStatus === 'Achieved') {
          expect(analysis.monthsToCoastFire).toBe(0);
        } else {
          expect(analysis.monthsToCoastFire).toBeGreaterThan(0);
        }
      });
    });
  });

  describe('Long-term Integration: WealthCalculator Service', () => {
    describe('Service Creation and Integration', () => {
      it('should have a WealthCalculator service that orchestrates formula usage', () => {
        // This will fail initially as WealthCalculator doesn't exist
        const calculator = tracker.getWealthCalculator();

        expect(calculator).toBeDefined();
        expect(calculator).toHaveProperty('calculateComprehensiveAnalysis');
        expect(calculator).toHaveProperty('projectScenarios');
        expect(calculator).toHaveProperty('optimizeContributions');
      });

      it('should provide comprehensive analysis using all formulas', () => {
        const calculator = tracker.getWealthCalculator();
        const analysis = calculator.calculateComprehensiveAnalysis();

        expect(analysis).toHaveProperty('currentStatus');
        expect(analysis).toHaveProperty('performance');
        expect(analysis).toHaveProperty('projections');
        expect(analysis).toHaveProperty('optimization');
        expect(analysis).toHaveProperty('coastFire');
        expect(analysis).toHaveProperty('scenarios');
      });

      it('should project multiple scenarios efficiently', () => {
        const calculator = tracker.getWealthCalculator();
        const scenarios = calculator.projectScenarios([1000000, 1500000, 2000000]);

        expect(scenarios).toHaveLength(3);
        scenarios.forEach(scenario => {
          expect(scenario).toHaveProperty('target');
          expect(scenario).toHaveProperty('conservative');
          expect(scenario).toHaveProperty('baseline');
          expect(scenario).toHaveProperty('optimistic');

          ['conservative', 'baseline', 'optimistic'].forEach(rate => {
            const projection = scenario[rate as keyof typeof scenario];
            expect(projection).toHaveProperty('months');
            expect(projection).toHaveProperty('date');
            expect(projection).toHaveProperty('futureValue');
            expect(projection).toHaveProperty('componentSplit');
          });
        });
      });

      it('should optimize contributions for different goals', () => {
        const calculator = tracker.getWealthCalculator();
        const optimization = calculator.optimizeContributions();

        expect(optimization).toHaveProperty('currentContribution');
        expect(optimization).toHaveProperty('recommendations');
        expect(optimization.recommendations).toHaveLength(9); // 3 targets × 3 timeframes

        optimization.recommendations.forEach(rec => {
          expect(rec).toHaveProperty('target');
          expect(rec).toHaveProperty('timeframe');
          expect(rec).toHaveProperty('requiredContribution');
          expect(rec).toHaveProperty('additionalNeeded');
          expect(rec).toHaveProperty('feasibility');
        });
      });
    });

    describe('Performance and Efficiency', () => {
      it('should cache calculations to avoid redundant formula calls', () => {
        const calculator = tracker.getWealthCalculator();

        // First call
        const start1 = performance.now();
        const analysis1 = calculator.calculateComprehensiveAnalysis();
        const time1 = performance.now() - start1;

        // Second call (should be faster due to caching)
        const start2 = performance.now();
        const analysis2 = calculator.calculateComprehensiveAnalysis();
        const time2 = performance.now() - start2;

        expect(analysis1).toEqual(analysis2);
        expect(time2).toBeLessThan(time1 * 0.5); // Should be at least 50% faster
      });

      it('should invalidate cache when portfolio data changes', () => {
        const calculator = tracker.getWealthCalculator();

        const analysis1 = calculator.calculateComprehensiveAnalysis();

        // Add new portfolio data
        tracker.addPortfolioData(2024, 12, 300000, 120000, 400000, 40000);

        const analysis2 = calculator.calculateComprehensiveAnalysis();

        // Results should be different
        expect(analysis1.currentStatus.totalValue).not.toBe(analysis2.currentStatus.totalValue);
      });
    });

    describe('Integration with Existing Methods', () => {
      it.skip('should enhance analyzeAndProject with comprehensive formula usage', async () => {
        // Mock console.log to capture output
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

        // Mock readline to avoid hanging
        const mockCreateInterface = jest.fn().mockReturnValue({
          question: jest.fn().mockImplementation((question, callback) => {
            callback(''); // Default empty response
          }),
          close: jest.fn()
        });
        jest.doMock('readline', () => ({
          createInterface: mockCreateInterface
        }));

        await tracker.analyzeAndProject();

        const output = consoleSpy.mock.calls.map(call => call[0]).join('\n');

        // Should include new analysis sections
        expect(output).toContain('REQUIRED CONTRIBUTIONS ANALYSIS');
        expect(output).toContain('COMPONENT SPLIT ANALYSIS');
        expect(output).toContain('COAST-FIRE ANALYSIS');
        expect(output).toContain('OPTIMIZATION RECOMMENDATIONS');

        consoleSpy.mockRestore();
      });

      it('should maintain backward compatibility with existing output', () => {
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

        tracker.analyzeAndProject();

        const output = consoleSpy.mock.calls.map(call => call[0]).join('\n');

        // Should still include existing sections
        expect(output).toContain('CURRENT STATUS');
        expect(output).toContain('PERFORMANCE METRICS');
        expect(output).toContain('WEALTH MILESTONES');
        expect(output).toContain('PAYSLIP ANALYSIS');

        consoleSpy.mockRestore();
      });
    });
  });
});
