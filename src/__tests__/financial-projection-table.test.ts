import { generateFinancialProjectionTable } from '../financial-projection-table';

// Mock the console methods to avoid cluttering test output
const originalConsoleLog = console.log;
const originalConsoleTable = console.table;

beforeAll(() => {
  console.log = jest.fn();
  console.table = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
  console.table = originalConsoleTable;
});

describe('FinancialProjectionTable', () => {
  describe('Function Export', () => {
    it('should export generateFinancialProjectionTable function', () => {
      expect(typeof generateFinancialProjectionTable).toBe('function');
    });

    it('should generate projection table output', () => {
      const output = generateFinancialProjectionTable();
      expect(typeof output).toBe('string');
      expect(output.length).toBeGreaterThan(0);
    });

    it('should include table headers in output', () => {
      const output = generateFinancialProjectionTable();
      expect(output).toContain('Scenario');
      expect(output).toContain('Contribution p mo');
      expect(output).toContain('Date hit');
      expect(output).toContain('Months from now');
    });

    it('should include contribution amounts in output', () => {
      const output = generateFinancialProjectionTable();
      expect(output).toContain('6 250');
      expect(output).toContain('5 260');
      expect(output).toContain('4 200');
    });

    it('should include scenario names in output', () => {
      const output = generateFinancialProjectionTable();
      // Should contain various scenario names
      expect(output.length).toBeGreaterThan(100); // Substantial output
    });

    it('should include insights section', () => {
      const output = generateFinancialProjectionTable();
      expect(output).toContain('What the table tells you');
    });

    it('should be deterministic', () => {
      const output1 = generateFinancialProjectionTable();
      const output2 = generateFinancialProjectionTable();
      expect(output1).toBe(output2);
    });
  });

  describe('Output Content Validation', () => {
    it('should contain formatted currency amounts', () => {
      const output = generateFinancialProjectionTable();

      // Should contain formatted amounts with spaces
      expect(output).toMatch(/\$\d+ \d+/);
    });

    it('should contain date information', () => {
      const output = generateFinancialProjectionTable();

      // Should contain month/year patterns
      expect(output).toMatch(/\w+ \d{4}/); // Like "Oct 2026"
    });

    it('should contain delta information', () => {
      const output = generateFinancialProjectionTable();

      // Should contain delta indicators
      expect(output).toMatch(/[+–-]\d+|–/);
    });

    it('should contain multiple scenarios', () => {
      const output = generateFinancialProjectionTable();

      // Should be substantial content indicating multiple scenarios
      expect(output.split('\n').length).toBeGreaterThan(10);
    });

    it('should handle execution without errors', () => {
      expect(() => {
        generateFinancialProjectionTable();
      }).not.toThrow();
    });
  });

});
