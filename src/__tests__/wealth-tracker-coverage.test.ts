import { WealthTracker } from '../wealth-tracker';

describe('WealthTracker Coverage Tests', () => {
  let tracker: WealthTracker;

  beforeEach(() => {
    tracker = new WealthTracker();
  });

  describe('Configuration and Setup', () => {
    it('should initialize with default configuration', () => {
      const config = tracker.getConfig();
      expect(config).toBeDefined();
      expect(config.wealthTargets).toContain(1500000);
    });

    it('should update configuration', () => {
      const newConfig = {
        wealthTargets: [2000000, 3000000],
        defaultTarget: 2000000,
        defaultMonthlyContributions: [5000, 6000, 7000],
        returnScenarios: [
          { name: 'Conservative', rate: 0.05 },
          { name: 'Aggressive', rate: 0.12 }
        ]
      };
      
      tracker.updateConfig(newConfig);
      const config = tracker.getConfig();
      expect(config.defaultTarget).toBe(2000000);
      expect(config.wealthTargets).toEqual([2000000, 3000000]);
    });
  });

  describe('Portfolio Data Management', () => {
    it('should add portfolio data with all fields', () => {
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000, 100000);
      
      const recent = tracker.getMostRecentPortfolioEntry();
      expect(recent).toBeDefined();
      expect(recent!.fidelity).toBe(100000);
    });

    it('should add portfolio data without fidelity', () => {
      tracker.clearAllData(); // Clear initial data
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      
      const recent = tracker.getMostRecentPortfolioEntry();
      expect(recent).toBeDefined();
      expect(recent!.fidelity).toBe(0); // Default value is 0, not undefined
    });

    it('should get portfolio history', () => {
      tracker.clearAllData(); // Clear initial data
      tracker.addPortfolioData(2024, 1, 300000, 150000, 100000, 50000);
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      
      const history = tracker.getPortfolioHistory();
      expect(history).toHaveLength(2);
      expect(history[0].date).toBe('2024-01');
    });

    it('should return null for empty portfolio', () => {
      tracker.clearAllData(); // Clear initial data
      const recent = tracker.getMostRecentPortfolioEntry();
      expect(recent).toBeNull();
    });

    it('should return empty array for empty portfolio history', () => {
      tracker.clearAllData(); // Clear initial data
      const history = tracker.getPortfolioHistory();
      expect(history).toEqual([]);
    });
  });

  describe('Payslip Data Management', () => {
    it('should add payslip data', () => {
      tracker.clearAllData(); // Clear initial data
      tracker.addPayslip('2024-07-15', 8000, 6000, 1000, 500, 500);
      
      const payslips = tracker.getMostRecentPayslips(1);
      expect(payslips).toHaveLength(1);
      expect(payslips[0].date).toBe('2024-07-15');
    });

    it('should get limited payslips', () => {
      for (let i = 1; i <= 5; i++) {
        tracker.addPayslip(`2024-07-${i.toString().padStart(2, '0')}`, 8000, 6000, 1000, 500, 500);
      }
      
      const payslips = tracker.getMostRecentPayslips(3);
      expect(payslips).toHaveLength(3);
    });

    it('should get all payslips when limit exceeds count', () => {
      tracker.clearAllData(); // Clear initial data
      tracker.addPayslip('2024-07-15', 8000, 6000, 1000, 500, 500);
      
      const payslips = tracker.getMostRecentPayslips(10);
      expect(payslips).toHaveLength(1);
    });

    it('should return empty array for no payslips', () => {
      tracker.clearAllData(); // Clear initial data
      const payslips = tracker.getMostRecentPayslips(5);
      expect(payslips).toEqual([]);
    });
  });

  describe('Analysis Methods', () => {
    beforeEach(() => {
      // Clear initial data and add test data
      tracker.clearAllData();
      tracker.addPortfolioData(2024, 1, 300000, 150000, 100000, 50000);
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      tracker.addPayslip('2024-07-15', 8000, 6000, 1000, 500, 500);
    });

    it('should check if has sufficient data for analysis', () => {
      expect(tracker.hasSufficientDataForAnalysis()).toBe(true);
    });

    it('should return false for insufficient portfolio data', () => {
      const emptyTracker = new WealthTracker();
      emptyTracker.clearAllData(); // Clear initial data
      emptyTracker.addPayslip('2024-07-15', 8000, 6000, 1000, 500, 500);
      expect(emptyTracker.hasSufficientDataForAnalysis()).toBe(false);
    });

    it('should return false for insufficient payslip data', () => {
      const emptyTracker = new WealthTracker();
      emptyTracker.clearAllData(); // Clear initial data
      emptyTracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      expect(emptyTracker.hasSufficientDataForAnalysis()).toBe(false);
    });

    it('should calculate monthly contribution from payslips', () => {
      const contribution = tracker.calculateAverageMonthlyContribution();
      expect(contribution).toBeGreaterThan(0);
      expect(contribution).toBeGreaterThan(0); // Should calculate based on payslip data
    });

    it('should return 0 for no payslips', () => {
      const emptyTracker = new WealthTracker();
      emptyTracker.clearAllData(); // Clear initial data
      const contribution = emptyTracker.calculateAverageMonthlyContribution();
      expect(contribution).toBe(4000); // fallback value from config
    });

    it('should calculate IRR', () => {
      const irr = tracker.calculateIrr();
      expect(typeof irr).toBe('number');
      expect(irr).toBeGreaterThan(-1); // IRR should be reasonable
    });

    it('should calculate mean return', () => {
      const stats = tracker.calculateStatistics();
      const meanReturn = stats.meanReturn;
      expect(typeof meanReturn).toBe('number');
    });

    it('should calculate standard deviation', () => {
      const stats = tracker.calculateStatistics();
      const stdDev = stats.stdDev;
      expect(typeof stdDev).toBe('number');
      expect(stdDev).toBeGreaterThanOrEqual(0);
    });

    it('should project future with positive target', () => {
      const months = tracker.projectFuture(2000000, 0.007, 6000);
      expect(months).toBeGreaterThan(0);
      expect(Number.isInteger(months)).toBe(true);
    });

    it('should return 0 for zero or negative target', () => {
      const months1 = tracker.projectFuture(0, 0.007, 6000);
      expect(months1).toBe(0);
      
      const months2 = tracker.projectFuture(-100000, 0.007, 6000);
      expect(months2).toBe(0);
    });

    it('should handle zero interest rate in projection', () => {
      const months = tracker.projectFuture(2000000, 0, 6000);
      expect(months).toBeGreaterThan(0);
    });

    it('should handle zero contribution in projection', () => {
      const months = tracker.projectFuture(2000000, 0.007, 0);
      expect(months).toBeGreaterThan(0);
    });
  });

  describe('Data Validation and Edge Cases', () => {
    it('should handle invalid portfolio data gracefully', () => {
      // Negative values should still be stored
      tracker.addPortfolioData(2024, 7, -100000, 200000, 150000, 50000);
      
      const recent = tracker.getMostRecentPortfolioEntry();
      expect(recent).toBeDefined();
      expect(recent!.trow).toBe(-100000);
    });

    it('should handle invalid payslip data gracefully', () => {
      tracker.clearAllData(); // Clear initial data
      // Negative values should still be stored
      tracker.addPayslip('2024-07-15', -8000, 6000, 1000, 500, 500);
      
      const payslips = tracker.getMostRecentPayslips(1);
      expect(payslips).toHaveLength(1);
      expect(payslips[0].gross).toBe(-8000);
    });

    it('should handle extreme date values', () => {
      tracker.clearAllData(); // Clear initial data
      tracker.addPortfolioData(1900, 1, 100000, 50000, 25000, 25000);
      tracker.addPortfolioData(2100, 12, 200000, 100000, 50000, 50000);
      
      const history = tracker.getPortfolioHistory();
      expect(history).toHaveLength(2);
    });

    it('should handle extreme month values', () => {
      tracker.clearAllData(); // Clear initial data
      tracker.addPortfolioData(2024, 0, 100000, 50000, 25000, 25000);
      tracker.addPortfolioData(2024, 13, 200000, 100000, 50000, 50000);
      
      const history = tracker.getPortfolioHistory();
      expect(history).toHaveLength(2);
    });

    it('should handle very large portfolio values', () => {
      tracker.addPortfolioData(2024, 7, 1e10, 1e9, 1e8, 1e7);
      
      const recent = tracker.getMostRecentPortfolioEntry();
      expect(recent).toBeDefined();
      expect(recent!.trow).toBe(1e10);
    });

    it('should handle very small portfolio values', () => {
      tracker.addPortfolioData(2024, 7, 0.01, 0.02, 0.03, 0.04);
      
      const recent = tracker.getMostRecentPortfolioEntry();
      expect(recent).toBeDefined();
      expect(recent!.trow).toBe(0.01);
    });
  });

  describe('Complex Scenarios', () => {
    it('should handle multiple portfolio entries in same month', () => {
      tracker.clearAllData(); // Clear initial data
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      tracker.addPortfolioData(2024, 7, 450000, 220000, 160000, 55000);
      
      const history = tracker.getPortfolioHistory();
      expect(history).toHaveLength(2);
      
      const recent = tracker.getMostRecentPortfolioEntry();
      expect(recent!.trow).toBe(450000); // Should be the latest entry
    });

    it('should handle multiple payslips on same date', () => {
      tracker.clearAllData(); // Clear initial data
      tracker.addPayslip('2024-07-15', 8000, 6000, 1000, 500, 500);
      tracker.addPayslip('2024-07-15', 8500, 6200, 1100, 550, 550);
      
      const payslips = tracker.getMostRecentPayslips(5);
      expect(payslips).toHaveLength(2);
    });

    it('should calculate IRR with single data point', () => {
      const singleTracker = new WealthTracker();
      singleTracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      singleTracker.addPayslip('2024-07-15', 8000, 6000, 1000, 500, 500);
      
      const irr = singleTracker.calculateIrr();
      expect(typeof irr).toBe('number');
    });

    it('should handle projection with current value exceeding target', () => {
      tracker.addPortfolioData(2024, 7, 2000000, 1000000, 500000, 500000);
      
      const months = tracker.projectFuture(1500000, 0.007, 6000);
      expect(months).toBe(0); // Already at target
    });
  });
});
