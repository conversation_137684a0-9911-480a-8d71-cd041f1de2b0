import * as formulas from '../formulas';

describe('Formulas Edge Cases and Coverage Tests', () => {
  describe('futureValueWithContributions', () => {
    it('should handle zero interest rate', () => {
      const result = formulas.futureValueWithContributions(100000, 0, 1000, 12);
      expect(result).toBe(112000); // 100000 + 1000 * 12
    });

    it('should handle negative interest rate', () => {
      const result = formulas.futureValueWithContributions(100000, -0.01, 1000, 12);
      expect(result).toBeLessThanOrEqual(100000); // May be equal due to contributions offsetting negative growth
    });

    it('should handle zero months', () => {
      const result = formulas.futureValueWithContributions(100000, 0.01, 1000, 0);
      expect(result).toBe(100000);
    });
  });

  describe('monthsToTarget', () => {
    it('should return 0 when already at target', () => {
      const result = formulas.monthsToTarget(100000, 100000, 0.01, 1000);
      expect(result).toBe(0);
    });

    it('should return 0 when above target', () => {
      const result = formulas.monthsToTarget(150000, 100000, 0.01, 1000);
      expect(result).toBe(0);
    });

    it('should handle zero interest rate', () => {
      const result = formulas.monthsToTarget(100000, 150000, 0, 5000);
      expect(result).toBe(10); // (150000 - 100000) / 5000
    });

    it('should handle negative interest rate', () => {
      const result = formulas.monthsToTarget(100000, 150000, -0.001, 5000);
      expect(result).toBeGreaterThanOrEqual(10); // Might be exactly 10 for linear case
    });

    it('should handle zero contribution with positive rate', () => {
      const result = formulas.monthsToTarget(100000, 150000, 0.01, 0);
      expect(result).toBeGreaterThan(0);
    });
  });

  describe('internalRateOfReturn', () => {
    it('should calculate IRR for simple cash flows', () => {
      const cashFlows = [-100000, 10000, 10000, 10000, 110000];
      const irr = formulas.internalRateOfReturn(cashFlows);
      expect(irr).toBeGreaterThan(0);
      expect(irr).toBeLessThan(0.2);
    });

    it('should handle all positive cash flows', () => {
      const cashFlows = [100000, 10000, 10000, 10000];
      const irr = formulas.internalRateOfReturn(cashFlows);
      expect(typeof irr).toBe('number');
    });

    it('should handle single cash flow', () => {
      const cashFlows = [100000];
      const irr = formulas.internalRateOfReturn(cashFlows);
      expect(typeof irr).toBe('number');
    });
  });

  describe('calculatePortfolioIRR', () => {
    it('should calculate portfolio IRR with regular contributions', () => {
      const portfolioValues = [100000, 110000, 125000, 140000];
      const monthlyContribution = 1000;
      const irr = formulas.calculatePortfolioIRR(portfolioValues, monthlyContribution);
      expect(irr).toBeGreaterThan(0);
    });

    it('should handle single portfolio value', () => {
      const portfolioValues = [100000];
      const irr = formulas.calculatePortfolioIRR(portfolioValues, 1000);
      expect(irr).toBe(0);
    });

    it('should handle zero contributions', () => {
      const portfolioValues = [100000, 110000, 121000];
      const irr = formulas.calculatePortfolioIRR(portfolioValues, 0);
      expect(irr).toBeGreaterThan(0);
    });
  });

  describe('requiredContributionForTarget', () => {
    it('should calculate required contribution', () => {
      const pmt = formulas.requiredContributionForTarget(100000, 200000, 0.01, 24);
      expect(pmt).toBeGreaterThan(0);
      expect(pmt).toBeLessThan(10000);
    });

    it('should handle zero interest rate', () => {
      const pmt = formulas.requiredContributionForTarget(100000, 200000, 0, 20);
      expect(pmt).toBe(5000); // (200000 - 100000) / 20
    });

    it('should handle already at target', () => {
      const pmt = formulas.requiredContributionForTarget(200000, 200000, 0.01, 24);
      expect(Math.abs(pmt)).toBeLessThan(2500); // Should be reasonable for current target
    });

    it('should handle above target', () => {
      const pmt = formulas.requiredContributionForTarget(250000, 200000, 0.01, 24);
      expect(pmt).toBeLessThan(0);
    });
  });

  describe('monthlyToAnnualReturn', () => {
    it('should convert monthly to annual return', () => {
      const annual = formulas.monthlyToAnnualReturn(0.01);
      expect(annual).toBeCloseTo(0.1268, 4);
    });

    it('should handle zero return', () => {
      const annual = formulas.monthlyToAnnualReturn(0);
      expect(annual).toBe(0);
    });

    it('should handle negative return', () => {
      const annual = formulas.monthlyToAnnualReturn(-0.01);
      expect(annual).toBeLessThan(0);
    });
  });

  describe('contributionStrippedPeriodReturn', () => {
    it('should calculate period return with contribution', () => {
      const periodReturn = formulas.contributionStrippedPeriodReturn(100000, 110000, 5000, 1);
      expect(periodReturn).toBeGreaterThan(0);
      expect(periodReturn).toBeLessThan(0.1);
    });

    it('should handle zero contribution', () => {
      const periodReturn = formulas.contributionStrippedPeriodReturn(100000, 110000, 0, 1);
      expect(periodReturn).toBe(0.1); // 10% return
    });

    it('should handle zero start value', () => {
      const periodReturn = formulas.contributionStrippedPeriodReturn(0, 10000, 10000, 1);
      expect(periodReturn).toBe(0);
    });

    it('should handle multiple months', () => {
      const periodReturn = formulas.contributionStrippedPeriodReturn(100000, 125000, 10000, 3);
      expect(periodReturn).toBeGreaterThan(0);
    });
  });

  describe('calculateStatistics', () => {
    it('should calculate mean and standard deviation', () => {
      const returns = [0.01, 0.02, -0.01, 0.03, 0.015];
      const stats = formulas.calculateStatistics(returns);
      
      const expectedMean = (0.01 + 0.02 + (-0.01) + 0.03 + 0.015) / 5;
      expect(stats.mean).toBeCloseTo(expectedMean, 4);
      expect(stats.stdDev).toBeGreaterThan(0);
    });

    it('should handle empty array', () => {
      const stats = formulas.calculateStatistics([]);
      expect(stats.mean).toBe(0);
      expect(stats.stdDev).toBe(0);
    });

    it('should handle single value', () => {
      const stats = formulas.calculateStatistics([0.05]);
      expect(stats.mean).toBe(0.05);
      expect(stats.stdDev).toBe(0);
    });

    it('should handle all same values', () => {
      const stats = formulas.calculateStatistics([0.02, 0.02, 0.02]);
      expect(stats.mean).toBe(0.02);
      expect(stats.stdDev).toBe(0);
    });
  });

  describe('futureValueWithBonus', () => {
    it('should add bonus when crossing bonus month', () => {
      const fv = formulas.futureValueWithBonus(100000, 0.01, 5000, 20000, 12, 3);
      expect(fv).toBeGreaterThan(formulas.futureValueWithContributions(100000, 0.01, 5000, 12));
    });

    it('should handle zero bonus', () => {
      const fv = formulas.futureValueWithBonus(100000, 0.01, 5000, 0, 12, 3);
      expect(fv).toBe(formulas.futureValueWithContributions(100000, 0.01, 5000, 12));
    });

    it('should handle bonus month at year end', () => {
      const fv = formulas.futureValueWithBonus(100000, 0.01, 5000, 20000, 12, 12);
      expect(fv).toBeGreaterThan(100000);
    });

    it('should not add bonus when not crossing bonus month', () => {
      const currentMonth = new Date().getMonth() + 1;
      const bonusMonth = currentMonth === 12 ? 1 : currentMonth + 1;
      const fv = formulas.futureValueWithBonus(100000, 0.01, 5000, 20000, 1, bonusMonth);
      expect(fv).toBe(formulas.futureValueWithContributions(100000, 0.01, 5000, 1));
    });
  });

  describe('componentSplit', () => {
    it('should calculate component split correctly', () => {
      const split = formulas.componentSplit(100000, 0.01, 5000, 24);
      
      expect(split.FV_total).toBeGreaterThan(100000);
      expect(split.FV_from_PV).toBeGreaterThan(100000);
      expect(split.FV_from_contr).toBeGreaterThan(0);
      expect(split.share_contr).toBeGreaterThan(0);
      expect(split.share_contr).toBeLessThan(1);
    });

    it('should handle zero contribution', () => {
      const split = formulas.componentSplit(100000, 0.01, 0, 24);
      
      expect(split.FV_total).toBe(split.FV_from_PV);
      expect(split.FV_from_contr).toBe(0);
      expect(split.share_contr).toBe(0);
    });

    it('should handle zero interest rate', () => {
      const split = formulas.componentSplit(100000, 0, 5000, 24);
      
      expect(split.FV_total).toBe(220000); // 100000 + 5000 * 24
      expect(split.FV_from_PV).toBe(100000);
      expect(split.FV_from_contr).toBe(120000);
    });

    it('should handle zero present value', () => {
      const split = formulas.componentSplit(0, 0.01, 5000, 24);
      
      expect(split.FV_from_PV).toBe(0);
      expect(split.FV_total).toBe(split.FV_from_contr);
      expect(split.share_contr).toBe(1);
    });
  });

  describe('coastFireThreshold', () => {
    it('should calculate coast fire threshold', () => {
      const threshold = formulas.coastFireThreshold(0.005, 2800);
      expect(threshold).toBeGreaterThan(0);
      // Coast fire uses annual calculation, not simple division
      const expectedThreshold = (12 * 2800) / (Math.pow(1.005, 12) - 1);
      expect(threshold).toBeCloseTo(expectedThreshold, 0);
    });

    it('should handle zero interest rate', () => {
      const threshold = formulas.coastFireThreshold(0, 2800);
      expect(threshold).toBe(Infinity);
    });

    it('should handle zero expenses', () => {
      const threshold = formulas.coastFireThreshold(0.005, 0);
      expect(threshold).toBe(0);
    });
  });

  describe('calculateScenarioRates', () => {
    it('should calculate scenario rates', () => {
      const scenarios = formulas.calculateScenarioRates(0.02, 0.025, 0.01);
      
      expect(scenarios.conservative).toBeLessThan(scenarios.baseline);
      expect(scenarios.baseline).toBe(0.02);
      expect(scenarios.optimistic).toBe(0.025);
      expect(scenarios.conservative).toBeGreaterThanOrEqual(0.005);
    });

    it('should enforce minimum conservative rate', () => {
      const scenarios = formulas.calculateScenarioRates(0.006, 0.01, 0.008);
      
      expect(scenarios.conservative).toBeGreaterThanOrEqual(0.005);
    });

    it('should handle zero standard deviation', () => {
      const scenarios = formulas.calculateScenarioRates(0.02, 0.025, 0);
      
      expect(scenarios.conservative).toBe(0.02);
      expect(scenarios.baseline).toBe(0.02);
      expect(scenarios.optimistic).toBe(0.025);
    });
  });

  describe('formatCurrency', () => {
    it('should format currency correctly', () => {
      expect(formulas.formatCurrency(1234567.89)).toBe('$1,234,568');
      expect(formulas.formatCurrency(1000)).toBe('$1,000');
      expect(formulas.formatCurrency(0)).toBe('$0');
      expect(formulas.formatCurrency(-1000)).toBe('-$1,000');
    });
  });

  describe('formatPercentage', () => {
    it('should format percentage correctly', () => {
      expect(formulas.formatPercentage(0.1234)).toBe('12.34%');
      expect(formulas.formatPercentage(0.01)).toBe('1.00%');
      expect(formulas.formatPercentage(0)).toBe('0.00%');
      expect(formulas.formatPercentage(-0.05)).toBe('-5.00%');
    });
  });

  describe('calculateReturnRate', () => {
    it('should calculate return rate correctly', () => {
      const rate = formulas.calculateReturnRate(100000, 110000, 12);
      expect(rate).toBeGreaterThan(0);
      expect(rate).toBeLessThan(0.01); // Monthly rate should be small
    });

    it('should handle zero months', () => {
      const rate = formulas.calculateReturnRate(100000, 110000, 0);
      expect(rate).toBe(0);
    });

    it('should handle negative return', () => {
      const rate = formulas.calculateReturnRate(100000, 90000, 12);
      expect(rate).toBeLessThan(0);
    });

    it('should handle zero start value', () => {
      const rate = formulas.calculateReturnRate(0, 10000, 12);
      expect(rate).toBe(0);
    });
  });

  describe('Edge case combinations', () => {
    it('should handle extreme values in futureValueWithContributions', () => {
      const result = formulas.futureValueWithContributions(1e10, 0.001, 1e6, 240);
      expect(result).toBeGreaterThan(1e10);
      expect(isFinite(result)).toBe(true);
    });

    it('should handle very small values', () => {
      const result = formulas.futureValueWithContributions(0.01, 0.001, 0.001, 12);
      expect(result).toBeGreaterThan(0);
      expect(result).toBeLessThan(1);
    });

    it('should handle mixed positive and negative in portfolio IRR', () => {
      const portfolioValues = [100000, 90000, 110000, 95000, 120000];
      const irr = formulas.calculatePortfolioIRR(portfolioValues, 2000);
      expect(typeof irr).toBe('number');
      expect(isFinite(irr)).toBe(true);
    });
  });
});