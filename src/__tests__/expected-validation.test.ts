/**
 * Comprehensive validation test using expected.ts as source of truth
 * This test validates that our formulas produce exactly the expected results
 * from the source of truth data provided by the user.
 */

import * as formulas from '../formulas';
import { WealthTracker } from '../wealth-tracker';
import { expectedTestData } from './data/expected';

describe('Expected Data Validation - Source of Truth Tests', () => {
  const { meta, test_cases, formula_tests } = expectedTestData;

  describe('Meta Data Validation', () => {
    it('should use correct baseline values from expected data', () => {
      expect(meta.start_balance).toBe(786000);
      expect(meta.monthly_growth_rate).toBe(0.0239);
      expect(meta.start_date_ISO).toBe("2025-07-01");
    });
  });

  describe('Test Cases - Main Projection Validation', () => {
    test_cases.forEach((testCase, index) => {
      it(`should match expected results for case ${index + 1}: $${testCase.FV.toLocaleString()} target with $${testCase.PMT} PMT`, () => {
        const result = formulas.monthsToTarget(
          meta.start_balance,
          testCase.FV,
          meta.monthly_growth_rate,
          testCase.PMT
        );

        // Validate months to target
        expect(result).toBe(testCase.expect_months);

        // Validate delay calculation vs 6250 PMT
        if (testCase.PMT !== 6250) {
          const baseline_months = formulas.monthsToTarget(
            meta.start_balance,
            testCase.FV,
            meta.monthly_growth_rate,
            6250
          );
          const actual_delay = result - baseline_months;
          expect(actual_delay).toBe(testCase.expect_delay_vs_6250);
        }

        // Validate future value calculation
        const calculated_fv = formulas.futureValueWithContributions(
          meta.start_balance,
          meta.monthly_growth_rate,
          testCase.PMT,
          testCase.expect_months
        );
        
        // Should reach at least the target (within rounding)
        expect(calculated_fv).toBeGreaterThanOrEqual(testCase.FV - 1);
      });
    });
  });

  describe('Formula Tests - Detailed Validation', () => {
    describe('D-1: Future Value Formula', () => {
      const d1_tests = formula_tests.filter(test => test.formula === 'D-1');
      
      d1_tests.forEach(test => {
        it(`should match expected FV for ${test.name}`, () => {
          const { PV, r, PMT, n } = test.inputs;
          const result = formulas.futureValueWithContributions(PV!, r!, PMT!, n!);
          
          // Round to nearest dollar for comparison
          expect(Math.round(result)).toBe(test.expect_FV!);
        });
      });
    });

    describe('D-3: Months to Target Formula', () => {
      const d3_tests = formula_tests.filter(test => test.formula === 'D-3');
      
      d3_tests.forEach(test => {
        it(`should match expected months for ${test.name}`, () => {
          const { PV, FV, r, PMT } = test.inputs;
          const result = formulas.monthsToTarget(PV!, FV!, r!, PMT!);
          
          expect(result).toBe(test.expect_n!);
        });
      });
    });

    describe('D-4: Delay Between PMTs Formula', () => {
      const d4_tests = formula_tests.filter(test => test.formula === 'D-4');
      
      d4_tests.forEach(test => {
        it(`should match expected delay for ${test.name}`, () => {
          const { PV, FV, r, PMT_fast, PMT_slow } = test.inputs;
          const result = formulas.deltaMonthsBetweenContributions(PV!, FV!, r!, PMT_fast!, PMT_slow!);
          
          expect(result).toBe(test.expect_Δn!);
        });
      });
    });

    describe('F-6: Required PMT Formula', () => {
      const f6_tests = formula_tests.filter(test => test.formula === 'F-6');
      
      f6_tests.forEach(test => {
        it(`should match expected required PMT for ${test.name}`, () => {
          const { PV, FV, r, n } = test.inputs;
          const result = formulas.requiredContributionForTarget(PV!, FV!, r!, n!);
          
          expect(result).toBeCloseTo(test.expect_PMT!, 2);
        });
      });
    });

    describe('F-8: Component Split Formula', () => {
      const f8_tests = formula_tests.filter(test => test.formula === 'F-8');
      
      f8_tests.forEach(test => {
        it(`should match expected component split for ${test.name}`, () => {
          const { PV, r, PMT, n } = test.inputs;
          const result = formulas.componentSplit(PV!, r!, PMT!, n!);
          
          expect(result.FV_total).toBe(test.expect!.FV_total);
          expect(result.FV_from_PV).toBe(test.expect!.FV_from_PV);
          expect(result.FV_from_contr).toBe(test.expect!.FV_from_contr);
          expect(result.share_contr).toBeCloseTo(test.expect!.share_contr, 3);
        });
      });
    });

    describe('F-9: Coast-FIRE Threshold Formula', () => {
      const f9_tests = formula_tests.filter(test => test.formula === 'F-9');
      
      f9_tests.forEach(test => {
        it(`should match expected coast-fire threshold for ${test.name}`, () => {
          const { monthly_r, PMT } = test.inputs;
          const result = formulas.coastFireThreshold(monthly_r!, PMT!);
          
          expect(result).toBe(test.expect_PV_coast!);
        });
      });
    });
  });

  describe('WealthTracker Integration with Expected Data', () => {
    let tracker: WealthTracker;

    beforeEach(() => {
      tracker = new WealthTracker();
      tracker.clearAllData();
      
      // Set up tracker with expected test data baseline
      const now = new Date('2025-07-01');
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      
      // Distribute the start balance across accounts (approximation)
      const trow = Math.round(meta.start_balance * 0.35);
      const robinhood = Math.round(meta.start_balance * 0.15);
      const etrade = Math.round(meta.start_balance * 0.35);
      const teradata = meta.start_balance - trow - robinhood - etrade;
      
      tracker.addPortfolioData(year, month, trow, robinhood, etrade, teradata, 0);
      
      // Add a payslip entry to make analysis available
      tracker.addPayslip('2025-07-01', 10000, 7500, 1000, 500, 500);
    });

    it('should match expected baseline values when using tracker methods', () => {
      const recentEntry = tracker.getMostRecentPortfolioEntry();
      expect(recentEntry).toBeTruthy();
      
      const totalValue = recentEntry!.trow + recentEntry!.robinhood + 
                        recentEntry!.etrade + recentEntry!.teradata + 
                        (recentEntry!.fidelity || 0);
      
      expect(totalValue).toBe(meta.start_balance);
    });

    it('should produce correct projections using expected test data', () => {
      // Test a few key test cases using the tracker
      const testCase = test_cases.find(tc => tc.PMT === 6250 && tc.FV === 1000000);
      expect(testCase).toBeTruthy();
      
      const months = tracker.projectFuture(testCase!.FV, meta.monthly_growth_rate, testCase!.PMT);
      expect(months).toBe(testCase!.expect_months);
    });

    it('should validate that all test cases produce consistent results', () => {
      // Validate all test cases systematically
      test_cases.forEach(testCase => {
        const months = tracker.projectFuture(testCase.FV, meta.monthly_growth_rate, testCase.PMT);
        expect(months).toBe(testCase.expect_months);
      });
    });
  });

  describe('Cross-Formula Consistency', () => {
    it('should have consistent results between D-1 and D-3 formulas', () => {
      // If D-3 gives us n months, then D-1 with n months should give us at least FV
      const PV = meta.start_balance;
      const FV = 1500000;
      const r = meta.monthly_growth_rate;
      const PMT = 6250;
      
      const months = formulas.monthsToTarget(PV, FV, r, PMT);
      const calculated_fv = formulas.futureValueWithContributions(PV, r, PMT, months);
      
      expect(calculated_fv).toBeGreaterThanOrEqual(FV - 1);
    });

    it('should have consistent delay calculations', () => {
      // Test that delay calculations are symmetric
      const PV = meta.start_balance;
      const FV = 2000000;
      const r = meta.monthly_growth_rate;
      
      const months_6250 = formulas.monthsToTarget(PV, FV, r, 6250);
      const months_5260 = formulas.monthsToTarget(PV, FV, r, 5260);
      const delay = formulas.deltaMonthsBetweenContributions(PV, FV, r, 6250, 5260);
      
      expect(delay).toBe(months_5260 - months_6250);
    });

    it('should have consistent component split totals', () => {
      // Component split should add up correctly
      const PV = meta.start_balance;
      const r = meta.monthly_growth_rate;
      const PMT = 6250;
      const n = 24;
      
      const split = formulas.componentSplit(PV, r, PMT, n);
      const expected_total = formulas.futureValueWithContributions(PV, r, PMT, n);
      
      expect(split.FV_total).toBe(Math.round(expected_total));
      // Allow for small rounding differences (±1) in component split totals
      expect(Math.abs((split.FV_from_PV + split.FV_from_contr) - split.FV_total)).toBeLessThanOrEqual(1);
    });
  });
});