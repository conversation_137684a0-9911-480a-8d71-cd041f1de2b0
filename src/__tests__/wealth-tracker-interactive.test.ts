import { WealthTracker } from '../wealth-tracker';
import * as readline from 'readline';

// Mock readline
jest.mock('readline');

// Mock console methods
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

describe('WealthTracker Interactive Methods', () => {
  let tracker: WealthTracker;
  let consoleSpy: jest.SpyInstance;

  beforeEach(() => {
    tracker = new WealthTracker();
    consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleSpy.mockRestore();
  });

  afterAll(() => {
    console.log = originalConsoleLog;
    console.error = originalConsoleError;
  });

  describe('analyzeAndProject', () => {
    beforeEach(() => {
      // Add required data for analysis
      tracker.addPortfolioData(2024, 1, 300000, 150000, 100000, 50000);
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      tracker.addPayslip('2024-07-15', 8000, 6000, 800, 400, 400);
    });

    it('should perform complete analysis and projection', async () => {
      // Mock readline interface for interactive projection table
      const mockQuestion = jest.fn()
        .mockImplementationOnce((q, cb) => cb('1.5')) // target
        .mockImplementationOnce((q, cb) => cb('6000')) // contribution 1
        .mockImplementationOnce((q, cb) => cb('')); // finish contributions
      
      const mockClose = jest.fn();
      
      const mockReadlineInterface = {
        question: mockQuestion,
        close: mockClose
      };
      
      (readline.createInterface as jest.Mock).mockReturnValue(mockReadlineInterface);

      await tracker.analyzeAndProject();

      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('WEALTH TRACKER ANALYSIS'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('CURRENT STATUS'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('PERFORMANCE METRICS'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('WEALTH MILESTONES'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('REQUIRED CONTRIBUTIONS ANALYSIS'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('COMPONENT SPLIT ANALYSIS'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('COAST-FIRE ANALYSIS'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('OPTIMIZATION RECOMMENDATIONS'));
    });

    it('should display portfolio breakdown', async () => {
      await tracker.analyzeAndProject();

      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('T.Rowe Retirement'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('RobinHood'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('E*Trade'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Teradata 401k'));
    });

    it('should show performance metrics', async () => {
      await tracker.analyzeAndProject();

      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Internal Rate of Return'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Mean Monthly Return'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Standard Deviation'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Annualized IRR'));
    });

    it('should display wealth milestones for all targets', async () => {
      await tracker.analyzeAndProject();

      // Should show projections for default targets
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('$0.9 Million'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('$1.5 Million'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('$2.0 Million'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('$3.0 Million'));
    });

    it('should show scenario rates', async () => {
      await tracker.analyzeAndProject();

      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Conservative'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Baseline'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Optimistic'));
    });

    it('should display growth attribution', async () => {
      await tracker.analyzeAndProject();

      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('GROWTH ATTRIBUTION'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Total Growth'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('From Contributions'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('From Returns'));
    });

    it('should handle missing fidelity account', async () => {
      // Add portfolio data without fidelity
      const trackerNoFidelity = new WealthTracker();
      trackerNoFidelity.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      trackerNoFidelity.addPayslip('2024-07-15', 8000, 6000, 800, 400, 400);

      await trackerNoFidelity.analyzeAndProject();

      // Should not show fidelity line
      const fidelityLogs = consoleSpy.mock.calls
        .map(call => call[0])
        .filter(log => typeof log === 'string' && log.includes('Fidelity'));
      
      expect(fidelityLogs).toHaveLength(0);
    });
  });

  describe('showInteractiveFinancialProjectionTable', () => {
    beforeEach(() => {
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      tracker.addPayslip('2024-07-15', 8000, 6000, 800, 400, 400);
    });

    it('should handle user input for target and contributions', async () => {
      const mockQuestion = jest.fn()
        .mockImplementationOnce((q, cb) => cb('2.0')) // target
        .mockImplementationOnce((q, cb) => cb('5000')) // contribution 1
        .mockImplementationOnce((q, cb) => cb('6000')) // contribution 2
        .mockImplementationOnce((q, cb) => cb('')); // finish contributions
      
      const mockClose = jest.fn();
      
      (readline.createInterface as jest.Mock).mockReturnValue({
        question: mockQuestion,
        close: mockClose
      });

      await tracker.showInteractiveFinancialProjectionTable();

      expect(mockQuestion).toHaveBeenCalledTimes(4);
      expect(mockClose).toHaveBeenCalled();
    });

    it('should use defaults when no input provided', async () => {
      const mockQuestion = jest.fn()
        .mockImplementationOnce((q, cb) => cb('')) // default target
        .mockImplementationOnce((q, cb) => cb('')); // no contributions - use defaults
      
      const mockClose = jest.fn();
      
      (readline.createInterface as jest.Mock).mockReturnValue({
        question: mockQuestion,
        close: mockClose
      });

      await tracker.showInteractiveFinancialProjectionTable();

      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('$1.5M'));
    });

    it('should validate contribution input', async () => {
      const mockQuestion = jest.fn()
        .mockImplementationOnce((q, cb) => cb('1.5')) // target
        .mockImplementationOnce((q, cb) => cb('invalid')) // invalid contribution
        .mockImplementationOnce((q, cb) => cb('5000')) // valid contribution
        .mockImplementationOnce((q, cb) => cb('')); // finish
      
      const mockClose = jest.fn();
      
      (readline.createInterface as jest.Mock).mockReturnValue({
        question: mockQuestion,
        close: mockClose
      });

      await tracker.showInteractiveFinancialProjectionTable();

      expect(consoleSpy).toHaveBeenCalledWith('Please enter a valid positive number.');
    });

    it('should limit contributions to 5', async () => {
      const mockQuestion = jest.fn()
        .mockImplementationOnce((q, cb) => cb('1.5')) // target
        .mockImplementationOnce((q, cb) => cb('1000')) // contribution 1
        .mockImplementationOnce((q, cb) => cb('2000')) // contribution 2
        .mockImplementationOnce((q, cb) => cb('3000')) // contribution 3
        .mockImplementationOnce((q, cb) => cb('4000')) // contribution 4
        .mockImplementationOnce((q, cb) => cb('5000')) // contribution 5
        .mockImplementationOnce((q, cb) => cb('6000')); // should be ignored
      
      const mockClose = jest.fn();
      
      (readline.createInterface as jest.Mock).mockReturnValue({
        question: mockQuestion,
        close: mockClose
      });

      await tracker.showInteractiveFinancialProjectionTable();

      // Should stop after 5 contributions
      expect(mockQuestion).toHaveBeenCalledTimes(6); // 1 target + 5 contributions
    });

    it('should handle readline error gracefully', async () => {
      (readline.createInterface as jest.Mock).mockImplementation(() => {
        throw new Error('Readline error');
      });

      await tracker.showInteractiveFinancialProjectionTable();

      expect(consoleSpy).toHaveBeenCalledWith('Error getting input, using defaults...');
    });
  });

  describe('showFinancialProjectionTable', () => {
    beforeEach(() => {
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      tracker.addPayslip('2024-07-15', 8000, 6000, 800, 400, 400);
    });

    it('should show projection table with custom values', async () => {
      await tracker.showFinancialProjectionTable(2000000, [4000, 5000, 6000]);

      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('$2.0 million'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('4,000'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('5,000'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('6,000'));
    });

    it('should show all scenario types', async () => {
      await tracker.showFinancialProjectionTable();

      expect(consoleSpy).toHaveBeenCalledWith(expect.stringMatching(/Optimistic.*r ≈/));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringMatching(/Baseline.*IRR ≈/));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringMatching(/\"Reddit classic\"/));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringMatching(/Conservative.*σ‑discounted/));
    });

    it('should calculate delays correctly', async () => {
      await tracker.showFinancialProjectionTable(1500000, [5000, 6250]);

      // Should show delay calculations vs baseline (6250)
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Δ vs $6.25 k'));
    });

    it('should provide insights', async () => {
      await tracker.showFinancialProjectionTable(1500000, [4000, 5000, 6250]);

      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('What the table tells you'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Dropping from'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('In the optimistic market run'));
    });
  });

  describe('Private methods through public interface', () => {
    beforeEach(() => {
      tracker.addPortfolioData(2024, 1, 300000, 150000, 100000, 50000);
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      tracker.addPayslip('2024-03-15', 8000, 6000, 800, 400, 400); // March payslip
      tracker.addPayslip('2024-07-15', 8000, 6000, 800, 400, 400);
    });

    it('should calculate months elapsed correctly', () => {
      const returns = tracker.calculatePeriodReturns();
      expect(returns).toHaveLength(1); // Should have one period return
    });

    it('should detect bonus month crossing', () => {
      // Portfolio entries span from January to July, should cross March bonus month
      const returns = tracker.calculatePeriodReturns();
      expect(returns).toHaveLength(1);
      expect(returns[0]).toBeGreaterThan(-1);
    });

    it('should handle multiple bonus years', () => {
      // Add data spanning multiple years to test bonus crossing
      tracker.addPortfolioData(2025, 3, 450000, 220000, 160000, 60000);
      
      const returns = tracker.calculatePeriodReturns();
      expect(returns.length).toBeGreaterThan(1);
    });

    it('should format dates correctly', () => {
      // Test through showPortfolioHistory which uses formatDate
      tracker.showPortfolioHistory();
      
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('January'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('July'));
    });
  });

  describe('Configuration methods', () => {
    it('should update configuration and log success', () => {
      const newConfig = {
        defaultTarget: 2500000,
        monthlyExpenses: 3200,
        paychecksPerYear: 24
      };

      tracker.updateConfig(newConfig);

      expect(consoleSpy).toHaveBeenCalledWith('✅ Configuration updated successfully');
      
      const config = tracker.getConfig();
      expect(config.defaultTarget).toBe(2500000);
      expect(config.monthlyExpenses).toBe(3200);
      expect(config.paychecksPerYear).toBe(24);
    });

    it('should merge partial config with defaults', () => {
      const partialConfig = {
        defaultTarget: 3000000
      };

      tracker.updateConfig(partialConfig);
      
      const config = tracker.getConfig();
      expect(config.defaultTarget).toBe(3000000);
      expect(config.paychecksPerYear).toBe(26); // Should keep default
      expect(config.monthlyExpenses).toBe(2800); // Should keep default
    });
  });

  describe('showGrowthAttribution', () => {
    it('should display growth attribution correctly', async () => {
      tracker.addPortfolioData(2024, 1, 300000, 150000, 100000, 50000, 0);
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000, 100000);
      tracker.addPayslip('2024-07-15', 8000, 6000, 800, 400, 400);

      await tracker.analyzeAndProject();

      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('GROWTH ATTRIBUTION'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Total Growth'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('From Contributions'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('From Returns'));
    });

    it('should handle multiple years with bonuses', async () => {
      // Add data spanning multiple years
      tracker.addPortfolioData(2023, 1, 200000, 100000, 75000, 25000);
      tracker.addPortfolioData(2024, 1, 300000, 150000, 100000, 50000);
      tracker.addPortfolioData(2024, 7, 400000, 200000, 150000, 50000);
      tracker.addPayslip('2024-07-15', 8000, 6000, 800, 400, 400);

      await tracker.analyzeAndProject();

      // Should account for multiple year's bonuses
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('GROWTH ATTRIBUTION'));
    });
  });

  describe('crossesBonusMonth edge cases', () => {
    it('should handle year transitions correctly', () => {
      // Add portfolio data crossing year boundaries
      tracker.addPortfolioData(2023, 12, 300000, 150000, 100000, 50000);
      tracker.addPortfolioData(2024, 3, 350000, 175000, 125000, 50000);
      tracker.addPortfolioData(2024, 6, 400000, 200000, 150000, 50000);
      tracker.addPayslip('2024-06-15', 8000, 6000, 800, 400, 400);

      const returns = tracker.calculatePeriodReturns();
      expect(returns.length).toBeGreaterThan(0);
    });

    it('should handle same year bonus crossing', () => {
      tracker.addPortfolioData(2024, 2, 300000, 150000, 100000, 50000);
      tracker.addPortfolioData(2024, 4, 350000, 175000, 125000, 50000);
      tracker.addPayslip('2024-04-15', 8000, 6000, 800, 400, 400);

      const returns = tracker.calculatePeriodReturns();
      expect(returns.length).toBeGreaterThan(0);
    });
  });
});