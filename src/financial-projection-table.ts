/**
 * Financial Projection Table Generator
 *
 * Generates a comprehensive table showing how different monthly contribution amounts
 * affect the timeline to reach $1.5 million across various market scenarios.
 */

import * as formulas from './formulas';
import { WealthCalculator } from './services/wealth-calculator';
import { INITIAL_PORTFOLIO_DATA } from './data/portfolio-data';
import { INITIAL_PAYSLIP_DATA } from './data/payslip-data';

interface ScenarioData {
  name: string;
  rate: number; // Monthly rate as decimal
  description: string;
}

interface ProjectionResult {
  scenario: string;
  contribution: number;
  months: number;
  date: string;
  deltaVsBaseline: number | null;
}

interface TableRow {
  scenario: string;
  contributionFormatted: string;
  dateHit: string;
  monthsFromNow: number;
  deltaVsBaseline: string;
}

class FinancialProjectionTable {
  private readonly startBalance = 786000; // July 2025 balance
  private readonly target = 1500000; // $1.5M target
  private readonly startDate = new Date(2025, 6, 1); // July 2025 (month 6 = July)
  private readonly calculator: WealthCalculator;
  private scenarios: ScenarioData[] = [];

  // Monthly contribution amounts to test
  private readonly contributions = [6250, 5260, 4200];

  constructor() {
    this.calculator = new WealthCalculator(INITIAL_PORTFOLIO_DATA, INITIAL_PAYSLIP_DATA);
    this.initializeScenarios();
  }

  /**
   * Initialize scenarios with calculated rates from actual data
   */
  private initializeScenarios(): void {
    const metrics = this.calculator.getCurrentMetrics();
    const scenarioRates = formulas.calculateScenarioRates(
      metrics.irr,
      metrics.statistics.mean,
      metrics.statistics.stdDev
    );

    this.scenarios = [
      {
        name: 'Optimistic',
        rate: scenarioRates.optimistic,
        description: `r ≈ ${(scenarioRates.optimistic * 100).toFixed(2)} %/mo`
      },
      {
        name: 'Baseline',
        rate: scenarioRates.baseline,
        description: `IRR ≈ ${(scenarioRates.baseline * 100).toFixed(2)} %/mo`
      },
      {
        name: '"Reddit classic"',
        rate: Math.pow(1.07, 1/12) - 1, // 7%/yr compounded monthly
        description: '7 % / yr ≈ 0.57 %/mo'
      },
      {
        name: 'Conservative',
        rate: scenarioRates.conservative,
        description: `σ‑discounted ${(scenarioRates.conservative * 100).toFixed(2)} %/mo`
      }
    ];
  }

  /**
   * Calculate months from start date to target date
   */
  private calculateMonthsFromStart(months: number): number {
    return months;
  }

  /**
   * Calculate target date from months
   */
  private calculateTargetDate(months: number): string {
    const targetDate = new Date(this.startDate);
    targetDate.setMonth(targetDate.getMonth() + months);
    
    return targetDate.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long' 
    });
  }

  /**
   * Format contribution amount
   */
  private formatContribution(amount: number): string {
    return `$${amount.toLocaleString().replace(',', ' ')}`;
  }

  /**
   * Format delta vs baseline
   */
  private formatDelta(delta: number | null): string {
    if (delta === null) {
      return '–'; // em-dash for baseline reference
    }
    if (delta === 0) {
      return '0';
    }
    return delta > 0 ? `+${delta}` : `${delta}`;
  }

  /**
   * Calculate all projections
   */
  private calculateProjections(): ProjectionResult[] {
    const results: ProjectionResult[] = [];
    
    for (const scenario of this.scenarios) {
      // Calculate baseline (6250) first to get reference for deltas
      const baselineMonths = formulas.monthsToTarget(
        this.startBalance,
        this.target,
        scenario.rate,
        6250
      );
      
      for (const contribution of this.contributions) {
        const months = formulas.monthsToTarget(
          this.startBalance,
          this.target,
          scenario.rate,
          contribution
        );
        
        const date = this.calculateTargetDate(months);
        const deltaVsBaseline = contribution === 6250 ? null : months - baselineMonths;
        
        results.push({
          scenario: scenario.name,
          contribution,
          months,
          date,
          deltaVsBaseline
        });
      }
    }
    
    return results;
  }

  /**
   * Convert projections to formatted table rows
   */
  private formatTableRows(projections: ProjectionResult[]): TableRow[] {
    return projections.map(proj => ({
      scenario: proj.scenario,
      contributionFormatted: this.formatContribution(proj.contribution),
      dateHit: proj.date,
      monthsFromNow: proj.months,
      deltaVsBaseline: this.formatDelta(proj.deltaVsBaseline)
    }));
  }

  /**
   * Generate the complete financial projection table
   */
  public generateTable(): string {
    const projections = this.calculateProjections();

    // Group rows by scenario for better organization
    const groupedRows: { [scenario: string]: ProjectionResult[] } = {};
    for (const proj of projections) {
      if (!groupedRows[proj.scenario]) {
        groupedRows[proj.scenario] = [];
      }
      groupedRows[proj.scenario].push(proj);
    }

    let output = '';

    // Title and subtitle
    output += 'How much longer does cutting your monthly contribution delay the $1.5 million‑mark?\n\n';
    output += '(Start point = July 2025 balance $786 k)\n\n';

    // Table header
    output += 'Scenario ¹\tContribution p mo\tDate hit $1.5 M\tMonths from now\tΔ vs $6.25 k\n';

    // Table rows organized by scenario
    for (const scenario of this.scenarios) {
      const scenarioRows = groupedRows[scenario.name];
      if (!scenarioRows) continue;

      // Sort by contribution amount (descending)
      scenarioRows.sort((a, b) => b.contribution - a.contribution);

      // Add scenario header
      output += `${scenario.name}\n`;
      output += `(${scenario.description})\t`;

      for (let i = 0; i < scenarioRows.length; i++) {
        const row = scenarioRows[i];
        const contributionFormatted = this.formatContribution(row.contribution);
        const deltaFormatted = this.formatDelta(row.deltaVsBaseline);

        if (i === 0) {
          // First row - include the contribution and other data on same line as scenario
          output += `${contributionFormatted}\t${row.date}\t${row.months}\t${deltaFormatted}\n`;
        } else {
          // Subsequent rows - just the data with tab alignment
          output += `\t${contributionFormatted}\t${row.date}\t${row.months}\t${deltaFormatted}\n`;
        }
      }
    }

    return output;
  }

  /**
   * Generate insights summary
   */
  public generateInsights(): string {
    const projections = this.calculateProjections();

    // Find specific examples for insights
    const baselineScenario = projections.filter(p => p.scenario === 'Baseline');
    const baseline6250 = baselineScenario.find(p => p.contribution === 6250);
    const baseline5260 = baselineScenario.find(p => p.contribution === 5260);
    const baseline4200 = baselineScenario.find(p => p.contribution === 4200);

    const optimisticScenario = projections.filter(p => p.scenario === 'Optimistic');
    const optimistic6250 = optimisticScenario.find(p => p.contribution === 6250);
    const optimistic4200 = optimisticScenario.find(p => p.contribution === 4200);

    const conservativeScenario = projections.filter(p => p.scenario === 'Conservative');
    const conservative4200 = conservativeScenario.find(p => p.contribution === 4200);
    const conservative6250 = conservativeScenario.find(p => p.contribution === 6250);

    let insights = 'What the table tells you\n\n';

    if (baseline6250 && baseline5260) {
      const delta5260 = baseline5260.months - baseline6250.months;
      insights += `Dropping from $6.25 k to $5.26 k only pushes the baseline date back ~${delta5260} months.\n\n`;
    }

    if (baseline6250 && baseline4200) {
      const delta4200 = baseline4200.months - baseline6250.months;
      let conservativeDelta = '';
      if (conservative6250 && conservative4200) {
        const conservativeDiff = conservative4200.months - conservative6250.months;
        conservativeDelta = `, up to ${conservativeDiff} months in the most cautious case`;
      }
      const monthText = delta4200 === 1 ? 'month' : 'months';
      insights += `Cutting further to $4.2 k stretches the journey by ~${delta4200} ${monthText} baseline${conservativeDelta}.\n\n`;
    }

    if (optimistic6250 && optimistic4200) {
      const optimisticDelta = optimistic4200.months - optimistic6250.months;
      insights += `In the optimistic market run, even a $2 k cut delays FIRE by just ${optimisticDelta === 1 ? 'one month' : optimisticDelta + ' months'}.\n`;
    }

    return insights;
  }

  /**
   * Generate the complete output
   */
  public generateCompleteOutput(): string {
    const table = this.generateTable();
    const insights = this.generateInsights();
    
    return table + insights;
  }
}

// Export the main function
export function generateFinancialProjectionTable(target?: number, contributions?: number[]): string {
  const generator = new FinancialProjectionTable();
  if (target !== undefined || contributions !== undefined) {
    // For now, return the default output since the class doesn't support custom parameters
    // TODO: Implement custom target and contributions support in FinancialProjectionTable
    return generator.generateCompleteOutput();
  }
  return generator.generateCompleteOutput();
}

// If run directly, output the table
if (require.main === module) {
  console.log(generateFinancialProjectionTable());
}
