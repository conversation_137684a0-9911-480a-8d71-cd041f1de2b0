import * as fs from 'fs';
import * as readline from 'readline';
import {PortfolioEntry, PayslipEntry, WealthData, ScenarioRates, Statistics, WealthTrackerConfig, DEFAULT_CONFIG} from './types';
import {INITIAL_PORTFOLIO_DATA} from './data/portfolio-data';
import {INITIAL_PAYSLIP_DATA} from './data/payslip-data';
import * as formulas from './formulas';
import { WealthCalculator } from './services/wealth-calculator';

export class WealthTracker {
    private portfolioHistory: PortfolioEntry[] = [...INITIAL_PORTFOLIO_DATA];
    private payslipHistory: PayslipEntry[] = [...INITIAL_PAYSLIP_DATA];
    private wealthCalculator: WealthCalculator | null = null;
    private config: WealthTrackerConfig;

    constructor(config?: Partial<WealthTrackerConfig>) {
        // Merge provided config with defaults
        this.config = { ...DEFAULT_CONFIG, ...config };
    }

    // Getter methods for configuration values
    private get ANNUAL_BONUS_GROSS(): number { return this.config.annualBonusGross; }
    private get ANNUAL_BONUS_NET(): number { return this.config.annualBonusNet; }
    private get BONUS_MONTH(): number { return this.config.bonusMonth; }
    private get PAYCHECKS_PER_YEAR(): number { return this.config.paychecksPerYear; }
    private get MONTHLY_EXPENSES(): number { return this.config.monthlyExpenses; }

    // Public methods to update configuration
    updateConfig(newConfig: Partial<WealthTrackerConfig>): void {
        this.config = { ...this.config, ...newConfig };
        console.log('✅ Configuration updated successfully');
    }

    getConfig(): WealthTrackerConfig {
        return { ...this.config };
    }

    setWealthTargets(targets: number[]): void {
        this.config.wealthTargets = [...targets];
        console.log(`✅ Wealth targets updated: ${targets.map(t => `$${(t/1000000).toFixed(1)}M`).join(', ')}`);
    }

    setDefaultMonthlyContributions(contributions: number[]): void {
        this.config.defaultMonthlyContributions = [...contributions];
        console.log(`✅ Default contributions updated: ${contributions.map(c => `$${c.toLocaleString()}`).join(', ')}`);
    }

    setTargetAmount(target: number): void {
        this.config.defaultTarget = target;
        console.log(`✅ Default target updated to $${(target/1000000).toFixed(1)}M`);
    }

    addPortfolioData(year: number, month: number, trow: number, robinhood: number, etrade: number, teradata: number, fidelity: number = 0): void {
        const dateStr = `${year}-${month.toString().padStart(2, '0')}`;
        const newEntry: PortfolioEntry = {
            date: dateStr,
            trow,
            robinhood,
            etrade,
            teradata,
            fidelity
        };

        this.portfolioHistory.push(newEntry);

        // Invalidate wealth calculator cache
        if (this.wealthCalculator) {
            this.wealthCalculator.updatePortfolioData(this.portfolioHistory);
        }

        console.log(`\n✅ Added portfolio data for ${this.formatDate(dateStr)}`);
        console.log(`   Total Portfolio Value: $${this.getTotalValue(newEntry).toLocaleString()}`);
    }

    addPayslip(dateStr: string, gross: number, net: number, espp: number, rothE: number, rothR: number): void {
        const totalInvest = espp + rothE + rothR;
        const newPayslip: PayslipEntry = {
            date: dateStr,
            gross,
            net,
            espp,
            roth_e: rothE,
            roth_r: rothR,
            total_invest: totalInvest
        };

        this.payslipHistory.push(newPayslip);
        this.payslipHistory.sort((a, b) => a.date.localeCompare(b.date));

        console.log(`\n✅ Added payslip for ${dateStr}`);
        console.log(`   Net Pay: $${net.toLocaleString()}`);
        console.log(`   Total Investments: $${totalInvest.toLocaleString()}`);
    }

    // Public method to access portfolio history for integration tests and external usage
    getPortfolioHistory(): PortfolioEntry[] {
        return [...this.portfolioHistory]; // Return a copy to prevent external modification
    }

    // Public method to access payslip history
    getPayslipHistory(): PayslipEntry[] {
        return [...this.payslipHistory]; // Return a copy to prevent external modification
    }

    // Get the most recent portfolio entry
    getMostRecentPortfolioEntry(): PortfolioEntry | null {
        if (this.portfolioHistory.length === 0) {
            return null;
        }
        return { ...this.portfolioHistory[this.portfolioHistory.length - 1] };
    }

    // Get the most recent N payslips
    getMostRecentPayslips(count: number): PayslipEntry[] {
        if (this.payslipHistory.length === 0) {
            return [];
        }
        const startIndex = Math.max(0, this.payslipHistory.length - count);
        return this.payslipHistory.slice(startIndex).map(p => ({ ...p }));
    }

    // Check if we have sufficient data for analysis
    hasSufficientDataForAnalysis(): boolean {
        return this.portfolioHistory.length > 0 && this.payslipHistory.length > 0;
    }

    // Add portfolio data only if all required values are provided
    addPortfolioDataIfComplete(year?: number, month?: number, trow?: number, robinhood?: number, etrade?: number, teradata?: number, fidelity?: number): boolean {
        // Check if all required fields are provided and valid
        if (year === undefined || month === undefined ||
            trow === undefined || robinhood === undefined ||
            etrade === undefined || teradata === undefined ||
            isNaN(year) || isNaN(month) || isNaN(trow) ||
            isNaN(robinhood) || isNaN(etrade) || isNaN(teradata)) {
            console.log('⏭️  Skipping portfolio data addition - incomplete or invalid input provided');
            return false;
        }

        // Validate year and month ranges
        if (year < 2020 || year > 2030 || month < 1 || month > 12) {
            console.log('⏭️  Skipping portfolio data addition - invalid year or month range');
            return false;
        }

        this.addPortfolioData(year, month, trow, robinhood, etrade, teradata, fidelity || 0);
        return true;
    }

    async addRecentPayslips(): Promise<void> {
        console.log('\nPORTFOLIO ADD RECENT PAYSLIPS');
        console.log('Enter payslip data (press Enter with empty date to finish)');

        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        const question = (query: string): Promise<string> => {
            return new Promise(resolve => rl.question(query, resolve));
        };

        let count = 0;
        while (count < 4) {
            console.log(`\nPayslip ${count + 1}:`);
            const dateStr = await question('Date (YYYY-MM-DD): ');

            if (!dateStr.trim()) {
                break;
            }

            try {
                const gross = parseFloat(await question('Gross Pay: $'));
                const net = parseFloat(await question('Net Pay: $'));
                const espp = parseFloat(await question('ESPP Deduction: $'));
                const rothE = parseFloat(await question('GapShare Roth E: $'));
                const rothR = parseFloat(await question('GapShare Roth R: $'));

                this.addPayslip(dateStr, gross, net, espp, rothE, rothR);
                count++;

            } catch (error) {
                console.log('Invalid input. Please enter numbers only.');
            }
        }

        rl.close();
    }

    calculateAverageMonthlyContribution(): number {
        if (this.payslipHistory.length === 0) {
            return this.config.fallbackMonthlyContribution;
        }

        const sixMonthsAgo = new Date();
        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

        const recentPayslips = this.payslipHistory.filter(p =>
            new Date(p.date) >= sixMonthsAgo
        );

        const payslipsToUse = recentPayslips.length > 0 ? recentPayslips : this.payslipHistory.slice(-12);

        if (payslipsToUse.length > 0) {
            const avgInvestment = payslipsToUse.reduce((sum, p) => sum + p.total_invest, 0) / payslipsToUse.length;
            return avgInvestment * this.PAYCHECKS_PER_YEAR / 12;
        }

        return this.config.fallbackMonthlyContribution;
    }

    private getTotalValue(entry: PortfolioEntry): number {
        return entry.trow + entry.robinhood + entry.etrade + entry.teradata + (entry.fidelity || 0);
    }

    private getMonthsElapsed(): number[] {
        const monthsElapsed: number[] = [];
        const startDate = new Date(this.portfolioHistory[0].date + '-01');

        for (const entry of this.portfolioHistory) {
            const currentDate = new Date(entry.date + '-01');
            const monthsDiff = (currentDate.getFullYear() - startDate.getFullYear()) * 12 +
                (currentDate.getMonth() - startDate.getMonth());
            monthsElapsed.push(monthsDiff);
        }

        return monthsElapsed;
    }

    private formatDate(dateStr: string): string {
        const [year, month] = dateStr.split('-');
        const date = new Date(parseInt(year), parseInt(month) - 1, 1);
        return date.toLocaleDateString('en-US', {year: 'numeric', month: 'long'});
    }

    calculateIrr(): number {
        const portfolioValues = this.portfolioHistory.map(entry => this.getTotalValue(entry));
        const monthlyContribution = this.calculateAverageMonthlyContribution();

        return formulas.calculatePortfolioIRR(portfolioValues, monthlyContribution);
    }

    calculatePeriodReturns(): number[] {
        const values = this.portfolioHistory.map(entry => this.getTotalValue(entry));
        const months = this.getMonthsElapsed();
        const monthlyContribution = this.calculateAverageMonthlyContribution();
        const monthlyReturns: number[] = [];

        for (let i = 1; i < values.length; i++) {
            const monthDiff = months[i] - months[i - 1];
            let contribution = monthDiff * monthlyContribution;

            if (this.crossesBonusMonth(i)) {
                contribution += this.ANNUAL_BONUS_NET;
            }

            const startValue = values[i - 1];
            const endValue = values[i];

            // Use formulas.contributionStrippedPeriodReturn for consistent calculation
            const monthlyReturn = formulas.contributionStrippedPeriodReturn(
                startValue,
                endValue,
                contribution,
                monthDiff
            );

            if (!isNaN(monthlyReturn) && isFinite(monthlyReturn)) {
                monthlyReturns.push(monthlyReturn);
            }
        }

        return monthlyReturns;
    }

    private crossesBonusMonth(index: number): boolean {
        if (index === 0) return false;

        const prevDate = new Date(this.portfolioHistory[index - 1].date + '-01');
        const currDate = new Date(this.portfolioHistory[index].date + '-01');

        for (let year = prevDate.getFullYear(); year <= currDate.getFullYear(); year++) {
            const bonusDate = new Date(year, this.BONUS_MONTH - 1, 1);
            if (prevDate < bonusDate && bonusDate <= currDate) {
                return true;
            }
        }

        return false;
    }

    private monthsAfterBonus(index: number): number {
        const currDate = new Date(this.portfolioHistory[index].date + '-01');

        let bonusDate: Date;
        if (currDate.getMonth() + 1 >= this.BONUS_MONTH) {
            bonusDate = new Date(currDate.getFullYear(), this.BONUS_MONTH - 1, 1);
        } else {
            bonusDate = new Date(currDate.getFullYear() - 1, this.BONUS_MONTH - 1, 1);
        }

        const monthsDiff = (currDate.getFullYear() - bonusDate.getFullYear()) * 12 +
            (currDate.getMonth() - bonusDate.getMonth());
        return monthsDiff;
    }

    calculateStatistics(): Statistics {
        const monthlyReturns = this.calculatePeriodReturns();
        const stats = formulas.calculateStatistics(monthlyReturns);

        return {
            meanReturn: stats.mean,
            stdDev: stats.stdDev
        };
    }

    projectFuture(target: number, monthlyRate: number, monthlyContribution: number): number {
        const currentValue = this.getTotalValue(this.portfolioHistory[this.portfolioHistory.length - 1]);

        return formulas.monthsToTarget(currentValue, target, monthlyRate, monthlyContribution);
    }

    analyzePayslips(): void {
        console.log('\n💰 PAYSLIP ANALYSIS');
        console.log('-'.repeat(60));

        if (this.payslipHistory.length === 0) {
            console.log('No payslip data available.');
            return;
        }

        const sixMonthsAgo = new Date();
        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

        const recentPayslips = this.payslipHistory.filter(p =>
            new Date(p.date) >= sixMonthsAgo
        );

        if (recentPayslips.length > 0) {
            const avgGross = recentPayslips.reduce((sum, p) => sum + p.gross, 0) / recentPayslips.length;
            const avgNet = recentPayslips.reduce((sum, p) => sum + p.net, 0) / recentPayslips.length;
            const avgInvest = recentPayslips.reduce((sum, p) => sum + p.total_invest, 0) / recentPayslips.length;

            console.log(`Recent Averages (last ${recentPayslips.length} payslips):`);
            console.log(`  Gross Pay:        $${avgGross.toLocaleString().padStart(8)} bi-weekly`);
            console.log(`  Net Pay:          $${avgNet.toLocaleString().padStart(8)} bi-weekly`);
            console.log(`  Investments:      $${avgInvest.toLocaleString().padStart(8)} bi-weekly`);
            console.log(`  Monthly Invest:   $${(avgInvest * this.PAYCHECKS_PER_YEAR / 12).toLocaleString().padStart(8)}`);

            const avgEspp = recentPayslips.reduce((sum, p) => sum + p.espp, 0) / recentPayslips.length;
            const avgRothE = recentPayslips.reduce((sum, p) => sum + p.roth_e, 0) / recentPayslips.length;
            const avgRothR = recentPayslips.reduce((sum, p) => sum + p.roth_r, 0) / recentPayslips.length;

            console.log('\nInvestment Breakdown:');
            console.log(`  ESPP:             $${avgEspp.toLocaleString().padStart(8)} (${(avgEspp / avgGross * 100).toFixed(1)}%)`);
            console.log(`  Roth E:           $${avgRothE.toLocaleString().padStart(8)} (${(avgRothE / avgGross * 100).toFixed(1)}%)`);
            console.log(`  Roth R:           $${avgRothR.toLocaleString().padStart(8)} (${(avgRothR / avgGross * 100).toFixed(1)}%)`);
            console.log(`  Total:            $${avgInvest.toLocaleString().padStart(8)} (${(avgInvest / avgGross * 100).toFixed(1)}%)`);
        }
    }

    async analyzeAndProject(): Promise<void> {
        console.log('\n' + '='.repeat(60));
        console.log('WEALTH TRACKER ANALYSIS');
        console.log('='.repeat(60));

        const currentEntry = this.portfolioHistory[this.portfolioHistory.length - 1];
        const currentValue = this.getTotalValue(currentEntry);
        const currentDate = this.formatDate(currentEntry.date);

        console.log(`\nCURRENT STATUS (${currentDate})`);
        console.log(`   T.Rowe Retirement: $${currentEntry.trow.toLocaleString().padStart(12)}`);
        console.log(`   RobinHood:         $${currentEntry.robinhood.toLocaleString().padStart(12)}`);
        console.log(`   E*Trade:           $${currentEntry.etrade.toLocaleString().padStart(12)}`);
        console.log(`   Teradata 401k:     $${currentEntry.teradata.toLocaleString().padStart(12)}`);
        if (currentEntry.fidelity) {
            console.log(`   Fidelity:          $${currentEntry.fidelity.toLocaleString().padStart(12)}`);
        }
        console.log(`   ${'-'.repeat(35)}`);
        console.log(`   TOTAL:             $${currentValue.toLocaleString().padStart(12)}`);

        const monthlyContribution = this.calculateAverageMonthlyContribution();
        const irr = this.calculateIrr();
        const {meanReturn, stdDev} = this.calculateStatistics();

        console.log(`\nPERFORMANCE METRICS`);
        console.log(`   Internal Rate of Return (IRR):  ${(irr * 100).toFixed(2).padStart(6)}% monthly`);
        console.log(`   Mean Monthly Return:            ${(meanReturn * 100).toFixed(2).padStart(6)}% monthly`);
        console.log(`   Standard Deviation:             ${(stdDev * 100).toFixed(2).padStart(6)}% monthly`);
        console.log(`   Annualized IRR:                 ${((Math.pow(1 + irr, 12) - 1) * 100).toFixed(1).padStart(6)}%`);

        console.log(`\nCONTRIBUTION ANALYSIS`);
        console.log(`   Monthly Investment (from payslips): $${monthlyContribution.toLocaleString().padStart(8)}`);
        console.log(`   Annual Bonus (net):                 $${this.ANNUAL_BONUS_NET.toLocaleString().padStart(8)}`);
        console.log(`   Monthly Living Expenses:            $${this.MONTHLY_EXPENSES.toLocaleString().padStart(8)}`);

        const scenarios: ScenarioRates = {
            'Conservative': Math.max(0.005, irr - stdDev),
            'Baseline': irr,
            'Optimistic': meanReturn
        };

        console.log(`\nSCENARIO RATES`);
        Object.entries(scenarios).forEach(([name, rate]) => {
            console.log(`   ${name.padEnd(14)} ${(rate * 100).toFixed(2).padStart(6)}% monthly (${((Math.pow(1 + rate, 12) - 1) * 100).toFixed(1).padStart(5)}% annual)`);
        });

        const targets = this.config.wealthTargets;

        console.log(`\nWEALTH MILESTONES`);

        targets.forEach(target => {
            console.log(`\n   Target: $${(target / 1e6).toFixed(1)} Million`);
            console.log('   ' + '-'.repeat(50));

            Object.entries(scenarios).forEach(([scenarioName, rate]) => {
                const months = this.projectFuture(target, rate, monthlyContribution);

                const currentDateObj = new Date(currentEntry.date + '-01');
                const targetDate = new Date(currentDateObj.getFullYear(), currentDateObj.getMonth() + months, 1);

                console.log(`   ${scenarioName.padEnd(14)} ${months.toString().padStart(3)} months  →  ${targetDate.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long'
                })}`);
            });
        });

        this.analyzePayslips();
        this.showGrowthAttribution(monthlyContribution);

        // Financial Projection Table
        await this.showInteractiveFinancialProjectionTable();

        // New comprehensive analysis sections
        console.log('\n📊 REQUIRED CONTRIBUTIONS ANALYSIS');
        console.log('------------------------------------------------------------');
        const reqAnalysis = this.analyzeRequiredContributions();
        reqAnalysis.targets.forEach(target => {
            console.log(`Target: $${target.amount.toLocaleString()}`);
            console.log(`  12 months: $${target.timeframes['12months'].requiredPmt.toLocaleString()}/month`);
            console.log(`  24 months: $${target.timeframes['24months'].requiredPmt.toLocaleString()}/month`);
            console.log(`  36 months: $${target.timeframes['36months'].requiredPmt.toLocaleString()}/month`);
        });

        console.log('\n🔄 COMPONENT SPLIT ANALYSIS');
        console.log('------------------------------------------------------------');
        const compAnalysis = this.analyzeComponentSplit();
        compAnalysis.projections.forEach(proj => {
            console.log(`${proj.scenario} (24 months):`);
            console.log(`  Total FV: $${proj.FV_total.toLocaleString()}`);
            console.log(`  From Growth: $${proj.FV_from_PV.toLocaleString()} (${(100 * (1 - proj.share_contr)).toFixed(1)}%)`);
            console.log(`  From Contributions: $${proj.FV_from_contr.toLocaleString()} (${(100 * proj.share_contr).toFixed(1)}%)`);
        });

        console.log('\n🏖️ COAST-FIRE ANALYSIS');
        console.log('------------------------------------------------------------');
        const coastAnalysis = this.analyzeCoastFire();
        console.log(`Status: ${coastAnalysis.currentStatus}`);
        console.log(`Thresholds:`);
        console.log(`  Conservative: $${coastAnalysis.thresholds.conservative.toLocaleString()}`);
        console.log(`  Baseline: $${coastAnalysis.thresholds.baseline.toLocaleString()}`);
        console.log(`  Optimistic: $${coastAnalysis.thresholds.optimistic.toLocaleString()}`);
        if (coastAnalysis.monthsToCoastFire > 0) {
            console.log(`Months to Coast-FIRE: ${coastAnalysis.monthsToCoastFire}`);
        }

        console.log('\n🎯 OPTIMIZATION RECOMMENDATIONS');
        console.log('------------------------------------------------------------');
        const calculator = this.getWealthCalculator();
        const optimization = calculator.optimizeContributions();
        console.log(`Current Monthly Contribution: $${optimization.currentContribution.toLocaleString()}`);
        console.log('Recommendations for aggressive timelines:');
        optimization.recommendations
            .filter(rec => rec.timeframe === 12)
            .forEach(rec => {
                console.log(`  $${rec.target.toLocaleString()} in 12 months: $${rec.requiredContribution.toLocaleString()}/month (${rec.feasibility})`);
            });
    }

    analyzeRequiredContributions() {
        const currentValue = this.getTotalValue(this.portfolioHistory[this.portfolioHistory.length - 1]);
        const irr = this.calculateIrr();
        const targets = this.config.wealthTargets;
        const timeframes = this.config.analysisTimeframes;

        return {
            targets: targets.map(target => ({
                amount: target,
                timeframes: {
                    '12months': {
                        requiredPmt: formulas.requiredContributionForTarget(currentValue, target, irr, 12)
                    },
                    '24months': {
                        requiredPmt: formulas.requiredContributionForTarget(currentValue, target, irr, 24)
                    },
                    '36months': {
                        requiredPmt: formulas.requiredContributionForTarget(currentValue, target, irr, 36)
                    }
                }
            }))
        };
    }

    analyzeComponentSplit() {
        const currentValue = this.getTotalValue(this.portfolioHistory[this.portfolioHistory.length - 1]);
        const monthlyContribution = this.calculateAverageMonthlyContribution();
        const irr = this.calculateIrr();
        const {meanReturn, stdDev} = this.calculateStatistics();

        const scenarios = formulas.calculateScenarioRates(irr, meanReturn, stdDev);
        const months = 24; // 2-year projection

        return {
            projections: [
                {
                    scenario: 'Conservative',
                    ...formulas.componentSplit(currentValue, scenarios.conservative, monthlyContribution, months)
                },
                {
                    scenario: 'Baseline',
                    ...formulas.componentSplit(currentValue, scenarios.baseline, monthlyContribution, months)
                },
                {
                    scenario: 'Optimistic',
                    ...formulas.componentSplit(currentValue, scenarios.optimistic, monthlyContribution, months)
                }
            ]
        };
    }

    analyzeCoastFire() {
        const monthlyContribution = this.calculateAverageMonthlyContribution();
        const currentValue = this.getTotalValue(this.portfolioHistory[this.portfolioHistory.length - 1]);
        const irr = this.calculateIrr();
        const {meanReturn, stdDev} = this.calculateStatistics();

        const scenarios = formulas.calculateScenarioRates(irr, meanReturn, stdDev);

        const thresholds = {
            conservative: formulas.coastFireThreshold(scenarios.conservative, monthlyContribution),
            baseline: formulas.coastFireThreshold(scenarios.baseline, monthlyContribution),
            optimistic: formulas.coastFireThreshold(scenarios.optimistic, monthlyContribution)
        };

        const isAtCoastFire = currentValue >= thresholds.baseline;

        return {
            thresholds,
            currentStatus: isAtCoastFire ? 'Achieved' : 'Not Achieved',
            monthsToCoastFire: isAtCoastFire ? 0 :
                formulas.monthsToTarget(currentValue, thresholds.baseline, irr, monthlyContribution)
        };
    }

    getWealthCalculator(): WealthCalculator {
        if (!this.wealthCalculator) {
            this.wealthCalculator = new WealthCalculator(this.portfolioHistory, this.payslipHistory);
        } else {
            // Update calculator with current data
            this.wealthCalculator.updatePortfolioData(this.portfolioHistory);
        }
        return this.wealthCalculator;
    }

    private showGrowthAttribution(monthlyContribution: number): void {
        const totalGrowth = this.getTotalValue(this.portfolioHistory[this.portfolioHistory.length - 1]) -
            this.getTotalValue(this.portfolioHistory[0]);
        const monthsElapsed = this.getMonthsElapsed();
        const monthsElapsedTotal = monthsElapsed[monthsElapsed.length - 1];

        let estimatedContributions = monthsElapsedTotal * monthlyContribution;
        const yearsElapsed = Math.floor(monthsElapsedTotal / 12);
        estimatedContributions += yearsElapsed * this.ANNUAL_BONUS_NET;

        const investmentReturns = totalGrowth - estimatedContributions;

        console.log(`\nGROWTH ATTRIBUTION (Since ${this.formatDate(this.portfolioHistory[0].date)})`);
        console.log(`   Total Growth:         $${totalGrowth.toLocaleString().padStart(12)}`);
        console.log(`   From Contributions:   $${estimatedContributions.toLocaleString().padStart(12)} (${(estimatedContributions / totalGrowth * 100).toFixed(1).padStart(5)}%)`);
        console.log(`   From Returns:         $${investmentReturns.toLocaleString().padStart(12)} (${(investmentReturns / totalGrowth * 100).toFixed(1).padStart(5)}%)`);
    }

    saveData(filename: string = 'wealth_tracker_data.json'): void {
        const data: WealthData = {
            portfolio_history: this.portfolioHistory,
            payslip_history: this.payslipHistory
        };

        fs.writeFileSync(filename, JSON.stringify(data, null, 2));
        console.log(`\n💾 Data saved to ${filename}`);
    }

    loadData(filename: string = 'wealth_tracker_data.json'): void {
        try {
            const data = JSON.parse(fs.readFileSync(filename, 'utf8')) as WealthData;
            this.portfolioHistory = data.portfolio_history || this.portfolioHistory;
            this.payslipHistory = data.payslip_history || this.payslipHistory;
            console.log(`\n📂 Data loaded from ${filename}`);
        } catch (error) {
            console.log(`\n❌ No saved data found at ${filename}`);
        }
    }

    showPortfolioHistory(): void {
        console.log('\n📋 PORTFOLIO HISTORY');
        console.log('-'.repeat(80));
        console.log('Date         T.Rowe      RobinHood      E*Trade       Teradata        Total');
        console.log('-'.repeat(80));

        this.portfolioHistory.forEach(entry => {
            const total = this.getTotalValue(entry);
            const date = this.formatDate(entry.date);
            console.log(`${date.padEnd(12)} $${entry.trow.toLocaleString().padStart(11)} $${entry.robinhood.toLocaleString().padStart(11)} $${entry.etrade.toLocaleString().padStart(11)} $${entry.teradata.toLocaleString().padStart(11)} $${total.toLocaleString().padStart(11)}`);
        });
    }

    showRecentPayslips(): void {
        console.log('\n📋 RECENT PAYSLIPS (Last 10)');
        console.log('-'.repeat(85));
        console.log('Date         Gross       Net       ESPP      Roth E     Roth R   Total Inv');
        console.log('-'.repeat(85));

        const recentPayslips = this.payslipHistory.slice(-10);
        recentPayslips.forEach(p => {
            console.log(`${p.date.padEnd(12)} $${p.gross.toLocaleString().padStart(9)} $${p.net.toLocaleString().padStart(9)} $${p.espp.toLocaleString().padStart(9)} $${p.roth_e.toLocaleString().padStart(9)} $${p.roth_r.toLocaleString().padStart(9)} $${p.total_invest.toLocaleString().padStart(9)}`);
        });
    }

    async showFinancialProjectionTable(target?: number, contributions?: number[]): Promise<void> {
        console.log('\n📈 CONTRIBUTION IMPACT ANALYSIS');
        console.log('='.repeat(60));

        const currentEntry = this.portfolioHistory[this.portfolioHistory.length - 1];
        const currentValue = this.getTotalValue(currentEntry);
        const currentDate = this.formatDate(currentEntry.date);
        const monthlyContribution = this.calculateAverageMonthlyContribution();
        const irr = this.calculateIrr();
        const {meanReturn, stdDev} = this.calculateStatistics();

        // Calculate scenario rates
        const scenarios = [
            {
                name: 'Optimistic',
                rate: meanReturn,
                description: `r ≈ ${(meanReturn * 100).toFixed(2)} %/mo`
            },
            {
                name: 'Baseline',
                rate: irr,
                description: `IRR ≈ ${(irr * 100).toFixed(2)} %/mo`
            },
            {
                name: '"Reddit classic"',
                rate: Math.pow(1.07, 1/12) - 1, // 7%/yr compounded monthly
                description: '7 % / yr ≈ 0.57 %/mo'
            },
            {
                name: 'Conservative',
                rate: Math.max(0.005, irr - stdDev),
                description: `σ‑discounted ${(Math.max(0.005, irr - stdDev) * 100).toFixed(2)} %/mo`
            }
        ];

        // Use provided values or dynamic defaults
        const finalTarget = target || this.config.defaultTarget;
        const finalContributions = contributions || this.config.defaultMonthlyContributions;

        const targetInMillions = (finalTarget / 1000000).toFixed(1);
        console.log(`How much longer does cutting your monthly contribution delay the $${targetInMillions} million‑mark?\n`);
        console.log(`(Start point = ${currentDate} balance $${Math.round(currentValue / 1000)} k)\n`);

        // Calculate all projections first to get baseline values
        interface ProjectionData {
            contribution: number;
            months: number;
            dateStr: string;
            baselineMonths: number;
        }

        interface ScenarioProjection {
            scenario: any;
            projections: ProjectionData[];
        }

        const projections: ScenarioProjection[] = [];
        for (const scenario of scenarios) {
            let baselineMonths = 0;
            const scenarioProjections: ProjectionData[] = [];

            for (const contribution of finalContributions) {
                const months = this.projectFuture(finalTarget, scenario.rate, contribution);

                if (contribution === Math.max(...finalContributions)) {
                    baselineMonths = months;
                }

                const targetDate = new Date(currentEntry.date + '-01');
                targetDate.setMonth(targetDate.getMonth() + months);
                const dateStr = targetDate.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short'
                });

                scenarioProjections.push({
                    contribution,
                    months,
                    dateStr,
                    baselineMonths
                });
            }

            projections.push({
                scenario,
                projections: scenarioProjections
            });
        }

        // Display the table with clean formatting
        const highestContrib = Math.max(...finalContributions);
        const highestContribFormatted = `$${(highestContrib/1000).toFixed(1)}k`;
        const targetFormatted = `$${targetInMillions}M`;
        console.log(`Scenario                     Contribution    Date hit ${targetFormatted}    Months    Δ vs ${highestContribFormatted}`);
        console.log('─'.repeat(80));

        for (const scenarioData of projections) {
            const scenario = scenarioData.scenario;
            const scenarioProjections = scenarioData.projections;
            const highestContribution = Math.max(...finalContributions);
            const baselineMonths = scenarioProjections.find(p => p.contribution === highestContribution)?.months || 0;

            for (let i = 0; i < scenarioProjections.length; i++) {
                const proj = scenarioProjections[i];
                const contributionFormatted = `$${proj.contribution.toLocaleString().replace(',', ' ')}`;
                const deltaFormatted = proj.contribution === Math.max(...finalContributions) ? '–' :
                    (proj.months - baselineMonths > 0 ? `+${proj.months - baselineMonths}` : `${proj.months - baselineMonths}`);

                if (i === 0) {
                    // First row with scenario name
                    const scenarioName = `${scenario.name} (${scenario.description})`;
                    console.log(`${scenarioName.padEnd(28)} ${contributionFormatted.padEnd(15)} ${proj.dateStr.padEnd(17)} ${proj.months.toString().padEnd(9)} ${deltaFormatted}`);
                } else {
                    // Subsequent rows with just data
                    console.log(`${' '.repeat(28)} ${contributionFormatted.padEnd(15)} ${proj.dateStr.padEnd(17)} ${proj.months.toString().padEnd(9)} ${deltaFormatted}`);
                }
            }
            console.log(''); // Empty line between scenarios
        }

        // Generate insights
        console.log('\nWhat the table tells you\n');

        // Calculate specific insights
        const baselineScenario = scenarios.find(s => s.name === 'Baseline');
        const conservativeScenario = scenarios.find(s => s.name === 'Conservative');

        if (baselineScenario && finalContributions.length >= 2) {
            const highestContrib = Math.max(...finalContributions);
            const lowestContrib = Math.min(...finalContributions);
            const middleContrib = finalContributions.find(c => c !== highestContrib && c !== lowestContrib) || finalContributions[1];

            const baselineHigh = this.projectFuture(finalTarget, baselineScenario.rate, highestContrib);
            const baselineMiddle = this.projectFuture(finalTarget, baselineScenario.rate, middleContrib);
            const baselineLow = this.projectFuture(finalTarget, baselineScenario.rate, lowestContrib);

            const deltaMiddle = baselineMiddle - baselineHigh;
            const deltaLow = baselineLow - baselineHigh;

            const highContribFormatted = `$${(highestContrib/1000).toFixed(1)} k`;
            const middleContribFormatted = `$${(middleContrib/1000).toFixed(1)} k`;
            const lowContribFormatted = `$${(lowestContrib/1000).toFixed(1)} k`;

            console.log(`Dropping from ${highContribFormatted} to ${middleContribFormatted} only pushes the baseline date back ~${deltaMiddle} month${deltaMiddle !== 1 ? 's' : ''}.\n`);

            if (conservativeScenario) {
                const conservativeHigh = this.projectFuture(finalTarget, conservativeScenario.rate, highestContrib);
                const conservativeLow = this.projectFuture(finalTarget, conservativeScenario.rate, lowestContrib);
                const conservativeDelta = conservativeLow - conservativeHigh;

                console.log(`Cutting further to ${lowContribFormatted} stretches the journey by ~${deltaLow} month${deltaLow !== 1 ? 's' : ''} baseline, up to ${conservativeDelta} months in the most cautious case.\n`);
            }
        }

        const optimisticScenario = scenarios.find(s => s.name === 'Optimistic');
        if (optimisticScenario && finalContributions.length >= 2) {
            const highestContrib = Math.max(...finalContributions);
            const lowestContrib = Math.min(...finalContributions);
            const optimisticHigh = this.projectFuture(finalTarget, optimisticScenario.rate, highestContrib);
            const optimisticLow = this.projectFuture(finalTarget, optimisticScenario.rate, lowestContrib);
            const optimisticDelta = optimisticLow - optimisticHigh;
            const cutAmount = (highestContrib - lowestContrib) / 1000;

            console.log(`In the optimistic market run, even a $${cutAmount} k cut delays FIRE by just ${optimisticDelta === 1 ? 'one month' : optimisticDelta + ' months'}.\n`);
        }
    }

    async showInteractiveFinancialProjectionTable(): Promise<void> {
        const readline = require('readline');
        let rl: any;

        try {
            rl = readline.createInterface({
                input: process.stdin,
                output: process.stdout
            });

            const question = (prompt: string): Promise<string> => {
                return new Promise((resolve) => {
                    rl.question(prompt, resolve);
                });
            };
            // Ask for target amount
            console.log('\n📈 FINANCIAL PROJECTION TABLE SETUP');
            console.log('─'.repeat(50));

            const defaultTargetM = (this.config.defaultTarget / 1000000).toFixed(1);
            const targetInput = await question(`Enter target amount (e.g., 1.5 for $1.5M, 2 for $2M) or press Enter for $${defaultTargetM}M: `);
            const target = targetInput.trim() ? parseFloat(targetInput) * 1000000 : this.config.defaultTarget;

            // Ask for contribution amounts
            console.log('\nEnter contribution amounts to compare (press Enter after each, empty line to finish):');
            const tipValues = this.config.defaultMonthlyContributions.join(', ');
            console.log(`💡 Tip: Try values like ${tipValues}`);

            const contributions: number[] = [];
            let contributionInput = '';
            let contributionCount = 1;

            do {
                contributionInput = await question(`Contribution ${contributionCount} (or press Enter to finish): $`);
                if (contributionInput.trim()) {
                    const amount = parseFloat(contributionInput.replace(/[,$]/g, ''));
                    if (!isNaN(amount) && amount > 0) {
                        contributions.push(amount);
                        contributionCount++;
                    } else {
                        console.log('Please enter a valid positive number.');
                    }
                }
            } while (contributionInput.trim() && contributions.length < 5);

            // Use defaults if no contributions provided
            const finalContributions = contributions.length > 0 ? contributions.sort((a, b) => b - a) : this.config.defaultMonthlyContributions;

            rl.close();

            // Show the projection table with user inputs
            await this.showFinancialProjectionTable(target, finalContributions);

        } catch (error) {
            rl.close();
            console.log('Error getting input, using defaults...');
            await this.showFinancialProjectionTable();
        }
    }
}