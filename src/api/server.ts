import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { WealthTracker } from '../wealth-tracker';
import { WealthTrackerConfig, PortfolioEntry, PayslipEntry } from '../types';

const app = express();
const PORT = process.env.PORT || 3001;

// Create wealth tracker instance
let wealthTracker = new WealthTracker();

// Function to set wealth tracker instance (for testing)
export const setWealthTracker = (tracker: WealthTracker) => {
  wealthTracker = tracker;
};

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://your-domain.com'] 
    : ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));

// Rate limiting (disabled in test environment)
if (process.env.NODE_ENV !== 'test') {
  const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.'
  });
  app.use('/api/', limiter);
}

// Body parsing middleware
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Configuration endpoints
app.get('/api/config', (req, res) => {
  try {
    const config = wealthTracker.getConfig();
    res.json({ success: true, data: config });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      error: 'Failed to get configuration',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.put('/api/config', (req, res) => {
  try {
    const newConfig: Partial<WealthTrackerConfig> = req.body;
    wealthTracker.updateConfig(newConfig);
    res.json({ success: true, message: 'Configuration updated successfully' });
  } catch (error) {
    res.status(400).json({ 
      success: false, 
      error: 'Failed to update configuration',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.put('/api/config/targets', (req, res) => {
  try {
    const { targets } = req.body;
    if (!Array.isArray(targets) || !targets.every(t => typeof t === 'number' && t > 0)) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid targets array. Must be array of positive numbers.' 
      });
    }
    wealthTracker.setWealthTargets(targets);
    res.json({ success: true, message: 'Wealth targets updated successfully' });
  } catch (error) {
    res.status(400).json({ 
      success: false, 
      error: 'Failed to update wealth targets',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.put('/api/config/contributions', (req, res) => {
  try {
    const { contributions } = req.body;
    if (!Array.isArray(contributions) || !contributions.every(c => typeof c === 'number' && c > 0)) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid contributions array. Must be array of positive numbers.' 
      });
    }
    wealthTracker.setDefaultMonthlyContributions(contributions);
    res.json({ success: true, message: 'Default contributions updated successfully' });
  } catch (error) {
    res.status(400).json({ 
      success: false, 
      error: 'Failed to update contributions',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.put('/api/config/target', (req, res) => {
  try {
    const { target } = req.body;
    if (typeof target !== 'number' || target <= 0) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid target. Must be a positive number.' 
      });
    }
    wealthTracker.setTargetAmount(target);
    res.json({ success: true, message: 'Default target updated successfully' });
  } catch (error) {
    res.status(400).json({ 
      success: false, 
      error: 'Failed to update target',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Portfolio endpoints
app.get('/api/portfolio', (req, res) => {
  try {
    const portfolioHistory = wealthTracker.getPortfolioHistory();
    res.json({ success: true, data: portfolioHistory });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      error: 'Failed to get portfolio data',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.post('/api/portfolio', (req, res) => {
  try {
    const { year, month, trow, robinhood, etrade, teradata, fidelity } = req.body;
    
    // Validate required fields
    if (!year || !month || trow === undefined || robinhood === undefined || 
        etrade === undefined || teradata === undefined) {
      return res.status(400).json({ 
        success: false, 
        error: 'Missing required fields: year, month, trow, robinhood, etrade, teradata' 
      });
    }
    
    const success = wealthTracker.addPortfolioDataIfComplete(
      year, month, trow, robinhood, etrade, teradata, fidelity || 0
    );
    
    if (success) {
      res.status(201).json({ success: true, message: 'Portfolio data added successfully' });
    } else {
      res.status(400).json({ success: false, error: 'Failed to add portfolio data - invalid input' });
    }
  } catch (error) {
    res.status(400).json({ 
      success: false, 
      error: 'Failed to add portfolio data',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.get('/api/portfolio/recent', (req, res) => {
  try {
    const recentEntry = wealthTracker.getMostRecentPortfolioEntry();
    res.json({ success: true, data: recentEntry });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      error: 'Failed to get recent portfolio data',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Payslip endpoints
app.get('/api/payslips', (req, res) => {
  try {
    const count = parseInt(req.query.count as string) || 10;
    const payslipHistory = wealthTracker.getMostRecentPayslips(count);
    res.json({ success: true, data: payslipHistory });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      error: 'Failed to get payslip data',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.post('/api/payslips', (req, res) => {
  try {
    const { date, gross, net, espp, rothE, rothR } = req.body;
    
    // Validate required fields
    if (!date || gross === undefined || net === undefined || 
        espp === undefined || rothE === undefined || rothR === undefined) {
      return res.status(400).json({ 
        success: false, 
        error: 'Missing required fields: date, gross, net, espp, rothE, rothR' 
      });
    }
    
    wealthTracker.addPayslip(date, gross, net, espp, rothE, rothR);
    res.status(201).json({ success: true, message: 'Payslip data added successfully' });
  } catch (error) {
    res.status(400).json({ 
      success: false, 
      error: 'Failed to add payslip data',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Analysis endpoints
app.get('/api/analysis/basic', (req, res) => {
  try {
    if (!wealthTracker.hasSufficientDataForAnalysis()) {
      return res.status(400).json({ 
        success: false, 
        error: 'Insufficient data for analysis. Please add portfolio and payslip data.' 
      });
    }
    
    const monthlyContribution = wealthTracker.calculateAverageMonthlyContribution();
    const irr = wealthTracker.calculateIrr();
    const statistics = wealthTracker.calculateStatistics();
    const recentEntry = wealthTracker.getMostRecentPortfolioEntry();
    
    const analysis = {
      monthlyContribution,
      irr,
      annualizedIRR: (Math.pow(1 + irr, 12) - 1),
      meanReturn: statistics.meanReturn,
      stdDev: statistics.stdDev,
      currentPortfolioValue: recentEntry ? (
        recentEntry.trow + recentEntry.robinhood + recentEntry.etrade + 
        recentEntry.teradata + (recentEntry.fidelity || 0)
      ) : 0
    };
    
    res.json({ success: true, data: analysis });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      error: 'Failed to perform analysis',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.get('/api/analysis/projections', (req, res) => {
  try {
    if (!wealthTracker.hasSufficientDataForAnalysis()) {
      return res.status(400).json({ 
        success: false, 
        error: 'Insufficient data for analysis. Please add portfolio and payslip data.' 
      });
    }
    
    const target = parseFloat(req.query.target as string) || wealthTracker.getConfig().defaultTarget;
    const contributions = req.query.contributions 
      ? (req.query.contributions as string).split(',').map(c => parseFloat(c.trim()))
      : wealthTracker.getConfig().defaultMonthlyContributions;
    
    const irr = wealthTracker.calculateIrr();
    const statistics = wealthTracker.calculateStatistics();
    const monthlyContribution = wealthTracker.calculateAverageMonthlyContribution();
    
    const scenarios = [
      {
        name: 'Optimistic',
        rate: statistics.meanReturn,
        description: `Mean return ≈ ${(statistics.meanReturn * 100).toFixed(2)}%/mo`
      },
      {
        name: 'Baseline', 
        rate: irr,
        description: `IRR ≈ ${(irr * 100).toFixed(2)}%/mo`
      },
      {
        name: 'Reddit Classic',
        rate: Math.pow(1.07, 1/12) - 1,
        description: '7%/yr ≈ 0.57%/mo'
      },
      {
        name: 'Conservative',
        rate: Math.max(0.005, irr - statistics.stdDev),
        description: `σ-discounted ${(Math.max(0.005, irr - statistics.stdDev) * 100).toFixed(2)}%/mo`
      }
    ];
    
    const projections = scenarios.map(scenario => ({
      scenario: scenario.name,
      description: scenario.description,
      contributions: contributions.map(contribution => {
        const months = wealthTracker.projectFuture(target, scenario.rate, contribution);
        return {
          monthlyAmount: contribution,
          monthsToTarget: months,
          years: (months / 12).toFixed(1)
        };
      })
    }));
    
    res.json({ 
      success: true, 
      data: {
        target,
        projections,
        currentValue: wealthTracker.getMostRecentPortfolioEntry()
      }
    });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      error: 'Failed to generate projections',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.get('/api/analysis/required-contributions', (req, res) => {
  try {
    if (!wealthTracker.hasSufficientDataForAnalysis()) {
      return res.status(400).json({ 
        success: false, 
        error: 'Insufficient data for analysis.' 
      });
    }
    
    const analysis = wealthTracker.analyzeRequiredContributions();
    res.json({ success: true, data: analysis });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      error: 'Failed to analyze required contributions',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.get('/api/analysis/component-split', (req, res) => {
  try {
    if (!wealthTracker.hasSufficientDataForAnalysis()) {
      return res.status(400).json({ 
        success: false, 
        error: 'Insufficient data for analysis.' 
      });
    }
    
    const analysis = wealthTracker.analyzeComponentSplit();
    res.json({ success: true, data: analysis });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      error: 'Failed to analyze component split',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.get('/api/analysis/coast-fire', (req, res) => {
  try {
    if (!wealthTracker.hasSufficientDataForAnalysis()) {
      return res.status(400).json({ 
        success: false, 
        error: 'Insufficient data for analysis.' 
      });
    }
    
    const analysis = wealthTracker.analyzeCoastFire();
    res.json({ success: true, data: analysis });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      error: 'Failed to analyze coast-fire',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Data management endpoints
app.post('/api/data/save', (req, res) => {
  try {
    const filename = req.body.filename || 'wealth_tracker_data.json';
    wealthTracker.saveData(filename);
    res.json({ success: true, message: `Data saved to ${filename}` });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      error: 'Failed to save data',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.post('/api/data/load', (req, res) => {
  try {
    const filename = req.body.filename || 'wealth_tracker_data.json';
    wealthTracker.loadData(filename);
    res.json({ success: true, message: `Data loaded from ${filename}` });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      error: 'Failed to load data',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.post('/api/data/reset', (req, res) => {
  try {
    // Create a new wealth tracker instance with current config
    const currentConfig = wealthTracker.getConfig();
    wealthTracker = new WealthTracker(currentConfig);
    // Clear all data to ensure empty state
    wealthTracker.clearAllData();
    res.json({ success: true, message: 'Data reset successfully' });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to reset data',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Enhanced data clearing with guided setup
app.post('/api/data/clear-and-setup', (req, res) => {
  try {
    const { portfolioValue, monthlyContribution } = req.body;

    // Validate required fields
    if (portfolioValue === undefined || monthlyContribution === undefined) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: portfolioValue, monthlyContribution'
      });
    }

    // Validate values
    if (portfolioValue < 0 || monthlyContribution < 0) {
      return res.status(400).json({
        success: false,
        error: 'Portfolio value and monthly contribution must be non-negative'
      });
    }

    // Create a new wealth tracker instance with current config
    const currentConfig = wealthTracker.getConfig();
    wealthTracker = new WealthTracker(currentConfig);
    // Clear all data to ensure empty state
    wealthTracker.clearAllData();

    // Add initial portfolio data for current month if portfolio value > 0
    if (portfolioValue > 0) {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;

      // Distribute portfolio value across accounts (using typical allocation)
      const trow = Math.round(portfolioValue * 0.6);
      const robinhood = Math.round(portfolioValue * 0.2);
      const etrade = Math.round(portfolioValue * 0.15);
      const teradata = portfolioValue - trow - robinhood - etrade;

      wealthTracker.addPortfolioData(year, month, trow, robinhood, etrade, teradata, 0);
    }

    res.json({
      success: true,
      message: 'Data cleared and initial setup completed',
      data: {
        portfolioValue,
        monthlyContribution,
        setupDate: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to clear and setup data',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Milestone projections endpoint
app.get('/api/analysis/milestone-projections', (req, res) => {
  try {
    if (!wealthTracker.hasSufficientDataForAnalysis()) {
      return res.status(400).json({
        success: false,
        error: 'Insufficient data for analysis. Please add portfolio and payslip data.'
      });
    }

    const target = parseFloat(req.query.target as string) || 1500000; // Default $1.5M
    const contributions = req.query.contributions
      ? (req.query.contributions as string).split(',').map(c => parseFloat(c.trim()))
      : [4200, 5260, 6250]; // Default contribution levels

    // Get current portfolio value
    const currentEntry = wealthTracker.getMostRecentPortfolioEntry();
    if (!currentEntry) {
      return res.status(400).json({
        success: false,
        error: 'No portfolio data available for milestone projections'
      });
    }

    const currentValue = currentEntry.trow + currentEntry.robinhood +
                        currentEntry.etrade + currentEntry.teradata + (currentEntry.fidelity || 0);

    // Calculate actual rates from portfolio data
    const irr = wealthTracker.calculateIrr();
    const statistics = wealthTracker.calculateStatistics();
    
    // Define scenarios with their rates
    const scenarios = [
      { name: 'Optimistic', rate: statistics.meanReturn, description: `${(statistics.meanReturn * 100).toFixed(2)}%/mo` },
      { name: 'Baseline', rate: irr, description: `${(irr * 100).toFixed(2)}%/mo` },
      { name: 'Reddit', rate: Math.pow(1.07, 1/12) - 1, description: '0.57%/mo' },
      { name: 'Conservative', rate: Math.max(0.005, irr - statistics.stdDev), description: `${(Math.max(0.005, irr - statistics.stdDev) * 100).toFixed(2)}%/mo` }
    ];

    // Calculate projections for each scenario and contribution level
    const projections = scenarios.map(scenario => {
      const contributionResults = contributions.map((contribution, index) => {
        const months = wealthTracker.projectFuture(target, scenario.rate, contribution);
        const hitDate = new Date();
        hitDate.setMonth(hitDate.getMonth() + months);

        // Calculate delay vs baseline contribution (6250)
        const baselineMonths = wealthTracker.projectFuture(target, scenario.rate, 6250);
        const delayVsBaseline = months - baselineMonths;

        return {
          monthlyContrib: contribution,
          hitDate: hitDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          months,
          delayVsBaseline: delayVsBaseline > 0 ? `+${delayVsBaseline}` : delayVsBaseline === 0 ? '–' : `${delayVsBaseline}`
        };
      });

      return {
        scenario: scenario.name,
        rate: scenario.rate * 12, // Convert to annual for display
        description: scenario.description,
        contributions: contributionResults
      };
    });

    res.json({
      success: true,
      data: {
        target,
        currentValue,
        startDate: new Date().toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        projections
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to generate milestone projections',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Error handling middleware
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  // Handle JSON parsing errors specifically
  if (error.type === 'entity.parse.failed' && error.statusCode === 400) {
    return res.status(400).json({
      success: false,
      error: 'Invalid JSON format',
      message: 'Request body must be valid JSON'
    });
  }

  // Handle other body-parser errors
  if (error.statusCode && error.statusCode < 500) {
    return res.status(error.statusCode).json({
      success: false,
      error: 'Bad Request',
      message: error.message || 'Invalid request format'
    });
  }

  // Log unexpected errors but don't expose details in production
  console.error('Unhandled error:', error);
  res.status(500).json({ 
    success: false, 
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ 
    success: false, 
    error: 'Endpoint not found',
    message: `Route ${req.method} ${req.originalUrl} not found`
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server
if (require.main === module) {
  app.listen(PORT, () => {
    console.log(`🚀 Wealth Tracker API server running on port ${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/health`);
    console.log(`🔧 API endpoints: http://localhost:${PORT}/api/*`);
    console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  });
}

export default app;