{"name": "er", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "start": "node dist/index.js", "api:dev": "ts-node src/api/server.ts", "api:build": "tsc && node dist/api/server.js", "api:start": "node dist/api/server.js", "frontend:dev": "cd frontend && npm start", "fullstack:dev": "concurrently \"npm run api:dev\" \"npm run frontend:dev\"", "fullstack:build": "npm run build && cd frontend && npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:frontend": "cd frontend && npm test"}, "dependencies": {"compression": "^1.7.4", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "readline": "^1.3.0"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@types/supertest": "^6.0.3", "concurrently": "^8.2.2", "jest": "^29.5.0", "supertest": "^7.1.3", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.5.3"}, "private": true}