
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/services</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/services</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">67.24% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>156/232</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">56.29% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>85/151</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">63.63% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>21/33</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">66.51% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>151/227</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="apiService.ts"><a href="apiService.ts.html">apiService.ts</a></td>
	<td data-value="60.68" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 60%"></div><div class="cover-empty" style="width: 40%"></div></div>
	</td>
	<td data-value="60.68" class="pct medium">60.68%</td>
	<td data-value="145" class="abs medium">88/145</td>
	<td data-value="48.23" class="pct low">48.23%</td>
	<td data-value="85" class="abs low">41/85</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="22" class="abs medium">11/22</td>
	<td data-value="60.13" class="pct medium">60.13%</td>
	<td data-value="143" class="abs medium">86/143</td>
	</tr>

<tr>
	<td class="file medium" data-value="mockApi.ts"><a href="mockApi.ts.html">mockApi.ts</a></td>
	<td data-value="78.16" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 78%"></div><div class="cover-empty" style="width: 22%"></div></div>
	</td>
	<td data-value="78.16" class="pct medium">78.16%</td>
	<td data-value="87" class="abs medium">68/87</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="66" class="abs medium">44/66</td>
	<td data-value="90.9" class="pct high">90.9%</td>
	<td data-value="11" class="abs high">10/11</td>
	<td data-value="77.38" class="pct medium">77.38%</td>
	<td data-value="84" class="abs medium">65/84</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-17T19:29:06.517Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    