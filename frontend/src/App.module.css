/* Main App Styles with Liquid Glass Design */

.app {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow-x: hidden;
}

.glassFilters {
  position: absolute;
  width: 0;
  height: 0;
  pointer-events: none;
}

.appContainer {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.appHeader {
  margin-bottom: 30px;
}

.headerGlass {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  text-align: center;
  overflow: hidden;
  position: relative;
  margin-bottom: 20px;
}

.headerGlass::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%);
  z-index: 1;
  pointer-events: none;
}

.glassContent {
  position: relative;
  z-index: 2;
  padding: 32px;
  color: #ffffff;
}

.headerGlass h1 {
  font-size: 2.8rem;
  font-weight: 700;
  margin: 0 0 12px 0;
  background: linear-gradient(135deg, #fff 0%, #f0f0f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  cursor: help;
  transition: transform 0.2s ease;
}

.headerGlass h1:hover {
  transform: scale(1.02);
}

.headerGlass p {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.navigation {
  display: flex;
  gap: 12px;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.navTab {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  background: transparent;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.navTab::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.navTab:hover {
  color: #ffffff;
  transform: translateY(-2px);
}

.navTab:hover::before {
  opacity: 1;
}

.navTab.active {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  color: #ffffff;
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.navTab.active::before {
  opacity: 1;
}

.appMain {
  flex: 1;
  margin-bottom: 30px;
}

.dashboardGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  align-items: start;
}

.formSection,
.analysisSection,
.projectionsSection,
.summarySection,
.historySection,
.chartsSection {
  min-height: 200px;
}

.summaryCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  position: relative;
}

.summaryCard::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%);
  z-index: 1;
  pointer-events: none;
}

.summaryCard .glassContent {
  position: relative;
  z-index: 2;
  padding: 24px;
}

.summaryCard h3 {
  color: #ffffff;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0 0 16px 0;
  cursor: help;
}

.summaryStats {
  display: flex;
  gap: 24px;
  justify-content: center;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  cursor: help;
  transition: transform 0.2s ease;
}

.stat:hover {
  transform: scale(1.05);
}

.statLabel {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 6px;
}

.statValue {
  color: #ffffff;
  font-size: 1.8rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.chartsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

.emptyChartsState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  text-align: center;
  padding: 40px;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.6;
}

.emptyChartsState h3 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.emptyChartsState p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  margin: 0 0 24px 0;
}

.switchToDashboard {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.switchToDashboard:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.appFooter {
  margin-top: auto;
  text-align: center;
}

.footerGlass {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.footerGlass::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.05) 0%, 
    rgba(255, 255, 255, 0.02) 100%);
  z-index: 1;
  pointer-events: none;
}

.footerGlass .glassContent {
  position: relative;
  z-index: 2;
  padding: 20px;
}

.footerGlass p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;
}

.footerGlass p:first-child {
  margin-bottom: 4px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboardGrid {
    grid-template-columns: 1fr;
  }
  
  .appContainer {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .headerGlass h1 {
    font-size: 2.2rem;
  }
  
  .headerGlass p {
    font-size: 1rem;
  }
  
  .glassContent {
    padding: 24px;
  }
  
  .navigation {
    flex-direction: column;
    gap: 8px;
  }
  
  .navTab {
    width: 100%;
    text-align: center;
  }
  
  .summaryStats {
    flex-direction: column;
    gap: 16px;
  }
  
  .dashboardGrid {
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .appContainer {
    padding: 12px;
  }
  
  .headerGlass h1 {
    font-size: 1.8rem;
  }
  
  .glassContent {
    padding: 20px;
  }
  
  .summaryCard .glassContent {
    padding: 16px;
  }
  
  .statValue {
    font-size: 1.5rem;
  }
}

/* Animation for glass elements */
@keyframes glassShimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}

.headerGlass:hover::before,
.summaryCard:hover::before,
.footerGlass:hover::before {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.05) 100%
  );
  background-size: 200% 100%;
  animation: glassShimmer 2s ease-in-out;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .headerGlass,
  .summaryCard,
  .footerGlass,
  .navigation {
    border-width: 2px;
    border-color: rgba(255, 255, 255, 0.5);
  }
  
  .navTab.active {
    background: rgba(255, 255, 255, 0.3);
  }
}