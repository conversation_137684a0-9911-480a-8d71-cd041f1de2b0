import {
  PortfolioData,
  PayslipData,
  WealthAnalysis,
  FinancialProjection,
  WealthTrackerData,
  ApiResponse
} from '../types';

// Mock data storage
let mockWealthData: WealthTrackerData = {
  portfolioData: [
    { assetType: 'stocks', value: 50000, description: 'Tech stocks portfolio' },
    { assetType: 'savings', value: 25000, description: 'Emergency fund' },
    { assetType: 'crypto', value: 15000, description: 'Bitcoin and Ethereum' }
  ],
  payslipData: [
    {
      grossSalary: 8000,
      netSalary: 6000,
      taxDeductions: 1500,
      benefits: 500,
      bonuses: 2000,
      payPeriod: 'monthly'
    }
  ]
};

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Mock API endpoints
export const mockApi = {
  // Add portfolio data
  addPortfolioData: async (data: PortfolioData): Promise<ApiResponse<PortfolioData>> => {
    await delay(500);
    
    try {
      // Validate portfolio data
      if (!data || typeof data !== 'object') {
        return {
          success: false,
          error: 'Invalid portfolio data',
          message: 'Portfolio data is required'
        };
      }
      
      if (data.value === undefined || data.value === null) {
        return {
          success: false,
          error: 'Missing value',
          message: 'Value is required'
        };
      }

      if (typeof data.value !== 'number' || data.value < 0) {
        return {
          success: false,
          error: 'Invalid value',
          message: 'Value must be positive'
        };
      }

      const validAssetTypes = ['stocks', 'bonds', 'crypto', 'real_estate', 'savings', 'other'];
      if (!data.assetType || !validAssetTypes.includes(data.assetType)) {
        return {
          success: false,
          error: 'Invalid asset type',
          message: 'Invalid asset type'
        };
      }
      
      mockWealthData.portfolioData.push({
        ...data,
        dateAdded: new Date()
      });
      
      return {
        success: true,
        data,
        message: 'Portfolio data added successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to add portfolio data',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  // Add payslip data
  addPayslipData: async (data: PayslipData): Promise<ApiResponse<PayslipData>> => {
    await delay(500);
    
    try {
      // Validate payslip data
      if (!data || typeof data !== 'object') {
        return {
          success: false,
          error: 'Invalid payslip data',
          message: 'Payslip data is required'
        };
      }
      
      if (typeof data.grossSalary !== 'number' || data.grossSalary < 0) {
        return {
          success: false,
          error: 'Invalid gross salary',
          message: 'Gross salary must be positive'
        };
      }
      
      if (typeof data.netSalary !== 'number' || data.netSalary < 0) {
        return {
          success: false,
          error: 'Invalid net salary',
          message: 'Net salary must be positive'
        };
      }
      
      if (typeof data.taxDeductions !== 'number' || data.taxDeductions < 0) {
        return {
          success: false,
          error: 'Invalid tax deductions',
          message: 'Tax deductions must be positive'
        };
      }
      
      if (typeof data.benefits !== 'number' || data.benefits < 0) {
        return {
          success: false,
          error: 'Invalid benefits',
          message: 'Benefits must be positive'
        };
      }
      
      const validPayPeriods = ['monthly', 'bi-weekly', 'weekly'];
      if (!data.payPeriod || !validPayPeriods.includes(data.payPeriod)) {
        return {
          success: false,
          error: 'Invalid pay period',
          message: 'Pay period must be one of: ' + validPayPeriods.join(', ')
        };
      }
      
      mockWealthData.payslipData.push({
        ...data,
        dateAdded: new Date()
      });
      
      return {
        success: true,
        data,
        message: 'Payslip data added successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to add payslip data',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },

  // Get wealth analysis
  getWealthAnalysis: async (): Promise<ApiResponse<WealthAnalysis>> => {
    await delay(800);
    
    try {
      const totalAssets = mockWealthData.portfolioData.reduce(
        (sum, asset) => sum + asset.value, 0
      );
      
      const monthlyIncome = mockWealthData.payslipData.reduce(
        (sum, payslip) => {
          const monthly = payslip.payPeriod === 'monthly' 
            ? payslip.netSalary
            : payslip.payPeriod === 'bi-weekly'
            ? payslip.netSalary * 2
            : payslip.netSalary * 4;
          return sum + monthly;
        }, 0
      ) / mockWealthData.payslipData.length;
      
      const annualIncome = monthlyIncome * 12;
      
      const assetBreakdown = mockWealthData.portfolioData.reduce(
        (breakdown, asset) => {
          breakdown[asset.assetType] = (breakdown[asset.assetType] || 0) + asset.value;
          return breakdown;
        }, {} as { [key: string]: number }
      );
      
      const analysis: WealthAnalysis = {
        totalAssets,
        monthlyIncome,
        annualIncome,
        savingsRate: (monthlyIncome * 0.2) / monthlyIncome, // Mock 20% savings rate
        netWorth: totalAssets - (annualIncome * 0.1), // Mock debt
        assetBreakdown
      };
      
      mockWealthData.analysis = analysis;
      
      return {
        success: true,
        data: analysis
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to analyze wealth data'
      };
    }
  },

  // Get financial projections
  getFinancialProjections: async (years: number): Promise<ApiResponse<FinancialProjection[]>> => {
    await delay(1000);
    
    try {
      let analysis = mockWealthData.analysis;
      if (!analysis) {
        const analysisResponse = await mockApi.getWealthAnalysis();
        if (!analysisResponse.success || !analysisResponse.data) {
          throw new Error('Failed to get analysis data');
        }
        analysis = analysisResponse.data;
      }
      
      const projections: FinancialProjection[] = [];
      
      for (let year = 1; year <= years; year++) {
        const growthRate = 0.07; // 7% annual growth
        const inflationRate = 0.03; // 3% inflation
        
        const projectedWealth = analysis.totalAssets * Math.pow(1 + growthRate, year);
        const projectedIncome = analysis.annualIncome * Math.pow(1 + inflationRate, year);
        const projectedSavings = projectedIncome * analysis.savingsRate;
        const projectedExpenses = projectedIncome - projectedSavings;
        
        projections.push({
          year,
          projectedWealth,
          projectedIncome,
          projectedSavings,
          projectedExpenses
        });
      }
      
      mockWealthData.projections = projections;
      
      return {
        success: true,
        data: projections
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to generate projections'
      };
    }
  },

  // Get all wealth data
  getWealthData: async (): Promise<ApiResponse<WealthTrackerData>> => {
    await delay(300);
    
    return {
      success: true,
      data: mockWealthData
    };
  },

  // Reset data (for testing)
  resetData: async (): Promise<ApiResponse<void>> => {
    await delay(200);
    
    mockWealthData = {
      portfolioData: [],
      payslipData: []
    };
    
    return {
      success: true,
      message: 'Data reset successfully'
    };
  }
};