import PayslipGenerator from '../utils/payslipGenerator';

describe('PayslipGenerator', () => {
  describe('generateFromPortfolio', () => {
    it('should generate realistic payslip data for $1M portfolio', () => {
      const payslips = PayslipGenerator.generateFromPortfolio({
        portfolioValue: 1000000,
        monthsOfData: 12,
        assumedAge: 35,
        assumedCareerLevel: 'mid'
      });

      expect(payslips).toHaveLength(12);
      
      // Check that all payslips have required fields
      payslips.forEach(payslip => {
        expect(payslip.id).toBeDefined();
        expect(payslip.grossSalary).toBeGreaterThan(0);
        expect(payslip.netSalary).toBeGreaterThan(0);
        expect(payslip.taxDeductions).toBeGreaterThan(0);
        expect(payslip.benefits).toBeGreaterThan(0);
        expect(payslip.payPeriod).toBe('monthly');
        expect(payslip.isGenerated).toBe(true);
        expect(['high', 'medium', 'low']).toContain(payslip.confidenceLevel);
      });

      // Net salary should be less than gross
      payslips.forEach(payslip => {
        expect(payslip.netSalary).toBeLessThan(payslip.grossSalary);
      });

      // For $1M portfolio at age 35, expect around $300k+ annual salary
      const averageGross = payslips.reduce((sum, p) => sum + p.grossSalary, 0) / payslips.length;
      const annualSalary = averageGross * 12;
      expect(annualSalary).toBeGreaterThan(200000);
      expect(annualSalary).toBeLessThan(600000);
    });

    it('should generate higher salaries for larger portfolios', () => {
      const smallPortfolioPayslips = PayslipGenerator.generateFromPortfolio({
        portfolioValue: 100000,
        monthsOfData: 6,
        assumedAge: 30
      });

      const largePortfolioPayslips = PayslipGenerator.generateFromPortfolio({
        portfolioValue: 2000000,
        monthsOfData: 6,
        assumedAge: 45
      });

      const smallAvgGross = smallPortfolioPayslips.reduce((sum, p) => sum + p.grossSalary, 0) / smallPortfolioPayslips.length;
      const largeAvgGross = largePortfolioPayslips.reduce((sum, p) => sum + p.grossSalary, 0) / largePortfolioPayslips.length;

      expect(largeAvgGross).toBeGreaterThan(smallAvgGross);
    });

    it('should adjust salaries based on career level', () => {
      const entryLevelPayslips = PayslipGenerator.generateFromPortfolio({
        portfolioValue: 500000,
        assumedCareerLevel: 'entry'
      });

      const executivePayslips = PayslipGenerator.generateFromPortfolio({
        portfolioValue: 500000,
        assumedCareerLevel: 'executive'
      });

      // Executive level should have higher average benefits due to higher base salary
      const entryAvgBenefits = entryLevelPayslips.reduce((sum, p) => sum + p.benefits, 0) / entryLevelPayslips.length;
      const executiveAvgBenefits = executivePayslips.reduce((sum, p) => sum + p.benefits, 0) / executivePayslips.length;

      // Allow for small random variations by comparing averages with tolerance
      expect(executiveAvgBenefits).toBeGreaterThan(entryAvgBenefits * 0.95);
    });

    it('should generate monthly variation in salary', () => {
      const payslips = PayslipGenerator.generateFromPortfolio({
        portfolioValue: 800000,
        monthsOfData: 12
      });

      const salaries = payslips.map(p => p.grossSalary);
      const uniqueSalaries = new Set(salaries);
      
      // Should have some variation (not all identical)
      expect(uniqueSalaries.size).toBeGreaterThan(1);
      
      // But variation shouldn't be too extreme (within ±10%)
      const avgSalary = salaries.reduce((sum, s) => sum + s, 0) / salaries.length;
      salaries.forEach(salary => {
        expect(salary).toBeGreaterThan(avgSalary * 0.9);
        expect(salary).toBeLessThan(avgSalary * 1.1);
      });
    });
  });

  describe('quickGenerate', () => {
    it('should generate 6 months of data with reasonable values', () => {
      const payslips = PayslipGenerator.quickGenerate(1200000);

      expect(payslips).toHaveLength(6);
      expect(payslips[0].isGenerated).toBe(true);
      expect(payslips[0].payPeriod).toBe('monthly');
      
      // Should have realistic salary for $1.2M portfolio
      const avgGross = payslips.reduce((sum, p) => sum + p.grossSalary, 0) / payslips.length;
      expect(avgGross).toBeGreaterThan(20000); // $240k+ annually
      expect(avgGross).toBeLessThan(50000); // Under $600k annually
    });
  });

  describe('generateRealisticPortfolioData', () => {
    it('should generate realistic portfolio breakdown', () => {
      const portfolioData = PayslipGenerator.generateRealisticPortfolioData();

      expect(portfolioData.length).toBeGreaterThan(0);
      
      // Check that portfolio adds up to around $900k
      const totalValue = portfolioData.reduce((sum, item) => sum + item.value, 0);
      expect(totalValue).toBe(900000);

      // Should have different asset types
      const assetTypes = new Set(portfolioData.map(item => item.assetType));
      expect(assetTypes.has('stocks')).toBe(true);
      expect(assetTypes.has('savings')).toBe(true);
      
      // Each item should have required fields
      portfolioData.forEach(item => {
        expect(item.id).toBeDefined();
        expect(item.assetType).toBeDefined();
        expect(item.value).toBeGreaterThan(0);
        expect(item.description).toBeDefined();
        expect(item.lastUpdated).toBeDefined();
      });
    });
  });

  describe('edge cases', () => {
    it('should handle very small portfolio values', () => {
      const payslips = PayslipGenerator.generateFromPortfolio({
        portfolioValue: 10000,
        assumedAge: 25
      });

      expect(payslips).toHaveLength(12);
      
      // Should still generate reasonable minimum salaries
      const avgGross = payslips.reduce((sum, p) => sum + p.grossSalary, 0) / payslips.length;
      expect(avgGross).toBeGreaterThan(3000); // At least $36k annually
    });

    it('should handle very large portfolio values', () => {
      const payslips = PayslipGenerator.generateFromPortfolio({
        portfolioValue: 10000000,
        assumedAge: 55
      });

      expect(payslips).toHaveLength(12);
      
      // Should cap at reasonable maximum salaries
      const avgGross = payslips.reduce((sum, p) => sum + p.grossSalary, 0) / payslips.length;
      const annualSalary = avgGross * 12;
      expect(annualSalary).toBeLessThan(600000); // Reasonable cap
    });

    it('should handle edge age values', () => {
      const youngPayslips = PayslipGenerator.generateFromPortfolio({
        portfolioValue: 500000,
        assumedAge: 22
      });

      const olderPayslips = PayslipGenerator.generateFromPortfolio({
        portfolioValue: 500000,
        assumedAge: 65
      });

      expect(youngPayslips).toHaveLength(12);
      expect(olderPayslips).toHaveLength(12);
      
      // Both should generate valid data
      expect(youngPayslips[0].grossSalary).toBeGreaterThan(0);
      expect(olderPayslips[0].grossSalary).toBeGreaterThan(0);
    });
  });

  describe('confidence levels', () => {
    it('should assign high confidence for typical portfolio-to-salary ratios', () => {
      // $300k portfolio with expected ~$100k salary (3x ratio) should be high confidence
      const payslips = PayslipGenerator.generateFromPortfolio({
        portfolioValue: 300000,
        assumedAge: 35
      });

      expect(payslips[0].confidenceLevel).toBe('high');
    });

    it('should assign lower confidence for unusual ratios', () => {
      // Very high portfolio for young age should be lower confidence
      const payslips = PayslipGenerator.generateFromPortfolio({
        portfolioValue: 5000000,
        assumedAge: 25
      });

      // Since the algorithm caps salary and calculates ratio, it might still be reasonable
      expect(['high', 'medium', 'low']).toContain(payslips[0].confidenceLevel);
    });
  });
});