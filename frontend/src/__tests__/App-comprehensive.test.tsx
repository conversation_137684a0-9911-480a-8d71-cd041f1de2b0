import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import App from '../App';
import * as useWealthTrackerModule from '../hooks/useWealthTracker';

// Mock the useWealthTracker hook
const mockUseWealthTracker = {
  wealthData: null,
  analysis: null,
  projections: null,
  isLoading: false,
  isAddingPortfolio: false,
  isAddingPayslip: false,
  isAnalyzing: false,
  isProjecting: false,
  error: null,
  addPortfolioData: jest.fn(),
  addPayslipData: jest.fn(),
  loadWealthData: jest.fn(),
  resetData: jest.fn(),
  clearError: jest.fn(),
  analyzeWealth: jest.fn(),
  generateProjections: jest.fn(),
};

jest.mock('../hooks/useWealthTracker');
const mockUseWealthTrackerHook = useWealthTrackerModule.useWealthTracker as jest.MockedFunction<typeof useWealthTrackerModule.useWealthTracker>;

describe('App Comprehensive Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseWealthTrackerHook.mockReturnValue(mockUseWealthTracker);
  });

  describe('Initial Load', () => {
    it('should render main app structure', () => {
      render(<App />);
      
      expect(screen.getByText('💰 Wealth Tracker')).toBeInTheDocument();
      expect(screen.getByText('Beautiful financial dashboard with liquid glass design')).toBeInTheDocument();
      expect(screen.getByText('Built with React 19 & Liquid Glass Design ✨')).toBeInTheDocument();
      expect(screen.getByText('Your financial data stays private and secure 🔒')).toBeInTheDocument();
    });

    it('should call loadWealthData on mount', () => {
      render(<App />);
      expect(mockUseWealthTracker.loadWealthData).toHaveBeenCalledTimes(1);
    });

    it('should render all navigation tabs', () => {
      render(<App />);
      
      expect(screen.getByTestId('dashboard-tab')).toBeInTheDocument();
      expect(screen.getByTestId('history-tab')).toBeInTheDocument();
      expect(screen.getByTestId('charts-tab')).toBeInTheDocument();
      expect(screen.getByTestId('analysis-tab')).toBeInTheDocument();
      expect(screen.getByTestId('clear-data-tab')).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    it('should show loading spinner when loading', () => {
      mockUseWealthTrackerHook.mockReturnValue({
        ...mockUseWealthTracker,
        isLoading: true,
      });

      render(<App />);
      
      expect(screen.getByText('Loading your financial data...')).toBeInTheDocument();
    });

    it('should not show main content when loading', () => {
      mockUseWealthTrackerHook.mockReturnValue({
        ...mockUseWealthTracker,
        isLoading: true,
      });

      render(<App />);
      
      expect(screen.queryByText('📊 Portfolio Summary')).not.toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should display error message when there is an error', () => {
      const errorMessage = 'Failed to load data';
      mockUseWealthTrackerHook.mockReturnValue({
        ...mockUseWealthTracker,
        error: errorMessage,
      });

      render(<App />);
      
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    it('should allow clearing error', () => {
      const errorMessage = 'Failed to load data';
      mockUseWealthTrackerHook.mockReturnValue({
        ...mockUseWealthTracker,
        error: errorMessage,
      });

      render(<App />);
      
      const dismissButton = screen.getByRole('button', { name: /dismiss/i });
      fireEvent.click(dismissButton);
      
      expect(mockUseWealthTracker.clearError).toHaveBeenCalledTimes(1);
    });
  });

  describe('Tab Navigation', () => {
    it('should start with dashboard tab active', () => {
      render(<App />);
      
      const dashboardTab = screen.getByTestId('dashboard-tab');
      expect(dashboardTab).toHaveClass('active');
    });

    it('should switch to history tab', () => {
      render(<App />);
      
      const historyTab = screen.getByTestId('history-tab');
      fireEvent.click(historyTab);
      
      expect(historyTab).toHaveClass('active');
      expect(screen.getByTestId('dashboard-tab')).not.toHaveClass('active');
    });

    it('should switch to charts tab', () => {
      render(<App />);
      
      const chartsTab = screen.getByTestId('charts-tab');
      fireEvent.click(chartsTab);
      
      expect(chartsTab).toHaveClass('active');
    });

    it('should switch to analysis tab', () => {
      render(<App />);
      
      const analysisTab = screen.getByTestId('analysis-tab');
      fireEvent.click(analysisTab);
      
      expect(analysisTab).toHaveClass('active');
    });

    it('should switch to clear data tab', () => {
      render(<App />);
      
      const clearDataTab = screen.getByTestId('clear-data-tab');
      fireEvent.click(clearDataTab);
      
      expect(clearDataTab).toHaveClass('active');
    });
  });

  describe('Dashboard Tab Content', () => {
    it('should render wealth tracker form in dashboard', () => {
      render(<App />);
      
      expect(screen.getByText('Add Portfolio Data')).toBeInTheDocument();
    });

    it('should show portfolio summary when wealth data is available', () => {
      const mockWealthData = {
        portfolioData: [
          { id: '1', date: '2024-07', value: 100000 },
          { id: '2', date: '2024-08', value: 110000 },
        ],
        payslipData: [
          { id: '1', date: '2024-07-15', gross: 8000, net: 6000 },
        ],
      };

      mockUseWealthTrackerHook.mockReturnValue({
        ...mockUseWealthTracker,
        wealthData: mockWealthData,
      });

      render(<App />);
      
      expect(screen.getByText('📊 Portfolio Summary')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument(); // Portfolio data count
      expect(screen.getByText('1')).toBeInTheDocument(); // Payslip data count
    });

    it('should show analysis when available', () => {
      const mockAnalysis = {
        currentValue: 800000,
        monthlyContribution: 6000,
        irr: 0.02,
        projectedGrowth: 0.08,
      };

      mockUseWealthTrackerHook.mockReturnValue({
        ...mockUseWealthTracker,
        analysis: mockAnalysis,
      });

      render(<App />);
      
      expect(screen.getByText('Wealth Analysis')).toBeInTheDocument();
    });

    it('should show projections chart when projections are available', () => {
      const mockProjections = [
        { month: 1, value: 100000, scenario: 'Conservative' },
        { month: 12, value: 120000, scenario: 'Conservative' },
      ];

      mockUseWealthTrackerHook.mockReturnValue({
        ...mockUseWealthTracker,
        projections: mockProjections,
      });

      render(<App />);
      
      expect(screen.getByText('Wealth Projections')).toBeInTheDocument();
    });

    it('should handle analyze wealth button click', async () => {
      mockUseWealthTracker.analyzeWealth.mockResolvedValue(true);
      
      render(<App />);
      
      const analyzeButton = screen.getByText('Analyze Wealth');
      fireEvent.click(analyzeButton);
      
      await waitFor(() => {
        expect(mockUseWealthTracker.analyzeWealth).toHaveBeenCalledTimes(1);
      });
    });

    it('should generate projections after successful analysis', async () => {
      mockUseWealthTracker.analyzeWealth.mockResolvedValue(true);
      
      render(<App />);
      
      const analyzeButton = screen.getByText('Analyze Wealth');
      fireEvent.click(analyzeButton);
      
      await waitFor(() => {
        expect(mockUseWealthTracker.generateProjections).toHaveBeenCalledWith(10);
      });
    });

    it('should not generate projections if analysis fails', async () => {
      mockUseWealthTracker.analyzeWealth.mockResolvedValue(false);
      
      render(<App />);
      
      const analyzeButton = screen.getByText('Analyze Wealth');
      fireEvent.click(analyzeButton);
      
      await waitFor(() => {
        expect(mockUseWealthTracker.analyzeWealth).toHaveBeenCalledTimes(1);
      });
      
      expect(mockUseWealthTracker.generateProjections).not.toHaveBeenCalled();
    });
  });

  describe('History Tab Content', () => {
    it('should show historical data table', () => {
      render(<App />);
      
      const historyTab = screen.getByTestId('history-tab');
      fireEvent.click(historyTab);
      
      expect(screen.getByText('Historical Data')).toBeInTheDocument();
      expect(screen.getByText('Export')).toBeInTheDocument();
    });

    it('should display mock historical entries', () => {
      render(<App />);
      
      const historyTab = screen.getByTestId('history-tab');
      fireEvent.click(historyTab);
      
      expect(screen.getByText('Added tech stocks portfolio worth $50,000')).toBeInTheDocument();
      expect(screen.getByText('Monthly salary entry - $6,000 net')).toBeInTheDocument();
      expect(screen.getByText('Wealth analysis - $75,000 total assets')).toBeInTheDocument();
    });

    it('should handle entry click', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      render(<App />);
      
      const historyTab = screen.getByTestId('history-tab');
      fireEvent.click(historyTab);
      
      const entryRow = screen.getByText('Added tech stocks portfolio worth $50,000').closest('tr');
      expect(entryRow).toBeInTheDocument();
      
      if (entryRow) {
        fireEvent.click(entryRow);
        expect(consoleSpy).toHaveBeenCalledWith('Entry clicked:', expect.any(Object));
      }
      
      consoleSpy.mockRestore();
    });

    it('should handle export click', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      render(<App />);
      
      const historyTab = screen.getByTestId('history-tab');
      fireEvent.click(historyTab);
      
      const exportButton = screen.getByText('Export');
      fireEvent.click(exportButton);
      
      expect(consoleSpy).toHaveBeenCalledWith('Export clicked');
      
      consoleSpy.mockRestore();
    });
  });

  describe('Charts Tab Content', () => {
    it('should show empty state when no projections', () => {
      render(<App />);
      
      const chartsTab = screen.getByTestId('charts-tab');
      fireEvent.click(chartsTab);
      
      expect(screen.getByText('No Chart Data Available')).toBeInTheDocument();
      expect(screen.getByText('Add some financial data to see beautiful charts and projections.')).toBeInTheDocument();
    });

    it('should show go to dashboard button in empty state', () => {
      render(<App />);
      
      const chartsTab = screen.getByTestId('charts-tab');
      fireEvent.click(chartsTab);
      
      const goToDashboardButton = screen.getByText('Go to Dashboard');
      fireEvent.click(goToDashboardButton);
      
      expect(screen.getByTestId('dashboard-tab')).toHaveClass('active');
    });

    it('should show projections chart when data is available', () => {
      const mockProjections = [
        { month: 1, value: 100000, scenario: 'Conservative' },
        { month: 12, value: 120000, scenario: 'Conservative' },
      ];

      mockUseWealthTrackerHook.mockReturnValue({
        ...mockUseWealthTracker,
        projections: mockProjections,
      });

      render(<App />);
      
      const chartsTab = screen.getByTestId('charts-tab');
      fireEvent.click(chartsTab);
      
      expect(screen.getByText('Wealth Projections')).toBeInTheDocument();
    });
  });

  describe('Analysis Tab Content', () => {
    it('should show milestone projections tab', () => {
      render(<App />);
      
      const analysisTab = screen.getByTestId('analysis-tab');
      fireEvent.click(analysisTab);
      
      expect(screen.getByText('Milestone Projections @ Three Contribution Levels')).toBeInTheDocument();
    });
  });

  describe('Clear Data Tab Content', () => {
    it('should show data clearing tab', () => {
      render(<App />);
      
      const clearDataTab = screen.getByTestId('clear-data-tab');
      fireEvent.click(clearDataTab);
      
      expect(screen.getByText('Clear All Data')).toBeInTheDocument();
    });

    it('should call loadWealthData after data is cleared', () => {
      const mockOnDataCleared = jest.fn();
      
      render(<App />);
      
      const clearDataTab = screen.getByTestId('clear-data-tab');
      fireEvent.click(clearDataTab);
      
      // Simulate data clearing completion
      const clearButton = screen.getByText('Clear All Data');
      fireEvent.click(clearButton);
      
      // The DataClearingTab component should call onDataCleared prop
      // which should trigger loadWealthData
    });
  });

  describe('Chart Click Handling', () => {
    it('should switch to charts tab when chart is clicked', () => {
      const mockProjections = [
        { month: 1, value: 100000, scenario: 'Conservative' },
      ];

      mockUseWealthTrackerHook.mockReturnValue({
        ...mockUseWealthTracker,
        projections: mockProjections,
      });

      render(<App />);
      
      // Start on dashboard tab
      expect(screen.getByTestId('dashboard-tab')).toHaveClass('active');
      
      // Click on a chart element (simulated)
      const chartContainer = screen.getByText('Wealth Projections').closest('.projectionsSection');
      if (chartContainer) {
        fireEvent.click(chartContainer);
      }
      
      // Should still be on dashboard (chart click handler sets to charts tab)
      // but we can test the handleChartClick function indirectly
    });
  });

  describe('Responsive Design Elements', () => {
    it('should render SVG filters for glass effect', () => {
      render(<App />);
      
      const svgFilters = document.querySelector('svg');
      expect(svgFilters).toBeInTheDocument();
      
      const glassFilter = document.querySelector('#glass-distortion');
      expect(glassFilter).toBeInTheDocument();
    });

    it('should have proper CSS class structure', () => {
      render(<App />);
      
      const appContainer = document.querySelector('.app');
      expect(appContainer).toBeInTheDocument();
      
      const appMain = document.querySelector('.appMain');
      expect(appMain).toBeInTheDocument();
      
      const navigation = document.querySelector('.navigation');
      expect(navigation).toBeInTheDocument();
    });
  });

  describe('Tooltip Integration', () => {
    it('should render tooltips with correct information', () => {
      const mockWealthData = {
        portfolioData: [{ id: '1', date: '2024-07', value: 100000 }],
        payslipData: [{ id: '1', date: '2024-07-15', gross: 8000, net: 6000 }],
      };

      mockUseWealthTrackerHook.mockReturnValue({
        ...mockUseWealthTracker,
        wealthData: mockWealthData,
      });

      render(<App />);
      
      // Tooltips should be present but content hidden until hover
      const tooltipTriggers = document.querySelectorAll('[data-tooltip]');
      expect(tooltipTriggers.length).toBeGreaterThan(0);
    });
  });

  describe('Data Flow Integration', () => {
    it('should update analysis after form submission', async () => {
      mockUseWealthTracker.analyzeWealth.mockResolvedValue(true);
      
      render(<App />);
      
      // Find and fill the form
      const yearInput = screen.getByLabelText(/year/i);
      const monthInput = screen.getByLabelText(/month/i);
      
      fireEvent.change(yearInput, { target: { value: '2024' } });
      fireEvent.change(monthInput, { target: { value: '7' } });
      
      // Submit the form
      const submitButton = screen.getByText('Add Portfolio Data');
      fireEvent.click(submitButton);
      
      // Should trigger analysis
      await waitFor(() => {
        expect(mockUseWealthTracker.analyzeWealth).toHaveBeenCalled();
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels and roles', () => {
      render(<App />);
      
      const navigation = screen.getByRole('navigation');
      expect(navigation).toBeInTheDocument();
      
      const main = screen.getByRole('main');
      expect(main).toBeInTheDocument();
    });

    it('should support keyboard navigation', () => {
      render(<App />);
      
      const firstTab = screen.getByTestId('dashboard-tab');
      firstTab.focus();
      
      expect(document.activeElement).toBe(firstTab);
      
      // Test tab navigation
      fireEvent.keyDown(firstTab, { key: 'Tab' });
      
      const secondTab = screen.getByTestId('history-tab');
      expect(document.activeElement).toBe(secondTab);
    });
  });
});