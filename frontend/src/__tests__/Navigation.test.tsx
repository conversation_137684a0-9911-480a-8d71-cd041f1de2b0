import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import Navigation from '../components/Navigation';

describe('Navigation', () => {
  const mockOnTabChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all navigation tabs', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    expect(screen.getByTestId('nav-calculator')).toBeInTheDocument();
    expect(screen.getByTestId('nav-history')).toBeInTheDocument();
    expect(screen.getByTestId('nav-analysis')).toBeInTheDocument();
    expect(screen.getByText('Calculator')).toBeInTheDocument();
    expect(screen.getByText('History')).toBeInTheDocument();
    expect(screen.getByText('Analysis')).toBeInTheDocument();
  });

  it('highlights the active tab', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const calculatorTab = screen.getByTestId('nav-calculator');
    const historyTab = screen.getByTestId('nav-history');
    const analysisTab = screen.getByTestId('nav-analysis');
    
    expect(calculatorTab).toHaveClass('active');
    expect(historyTab).not.toHaveClass('active');
    expect(analysisTab).not.toHaveClass('active');
  });

  it('switches active tab when different tab is selected', () => {
    const { rerender } = render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    // Initially calculator is active
    expect(screen.getByTestId('nav-calculator')).toHaveClass('active');
    expect(screen.getByTestId('nav-history')).not.toHaveClass('active');
    
    // Re-render with history active
    rerender(<Navigation activeTab="history" onTabChange={mockOnTabChange} />);
    
    expect(screen.getByTestId('nav-calculator')).not.toHaveClass('active');
    expect(screen.getByTestId('nav-history')).toHaveClass('active');
  });

  it('calls onTabChange when tab is clicked', async () => {
    const user = userEvent.setup();
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const historyTab = screen.getByTestId('nav-history');
    await user.click(historyTab);
    
    expect(mockOnTabChange).toHaveBeenCalledWith('history');
  });

  it('calls onTabChange when calculator tab is clicked', async () => {
    const user = userEvent.setup();
    render(<Navigation activeTab="history" onTabChange={mockOnTabChange} />);
    
    const calculatorTab = screen.getByTestId('nav-calculator');
    await user.click(calculatorTab);
    
    expect(mockOnTabChange).toHaveBeenCalledWith('calculator');
  });

  it('calls onTabChange when analysis tab is clicked', async () => {
    const user = userEvent.setup();
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const analysisTab = screen.getByTestId('nav-analysis');
    await user.click(analysisTab);
    
    expect(mockOnTabChange).toHaveBeenCalledWith('analysis');
  });

  it('handles keyboard navigation', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const calculatorTab = screen.getByTestId('nav-calculator');
    const historyTab = screen.getByTestId('nav-history');
    const analysisTab = screen.getByTestId('nav-analysis');
    
    // Tabs should be focusable
    expect(calculatorTab).toHaveAttribute('tabIndex', '0');
    expect(historyTab).toHaveAttribute('tabIndex', '0');
    expect(analysisTab).toHaveAttribute('tabIndex', '0');
  });

  it('supports Enter key for tab activation', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const historyTab = screen.getByTestId('nav-history');
    fireEvent.keyDown(historyTab, { key: 'Enter', code: 'Enter' });
    
    expect(mockOnTabChange).toHaveBeenCalledWith('history');
  });

  it('supports Space key for tab activation', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const historyTab = screen.getByTestId('nav-history');
    fireEvent.keyDown(historyTab, { key: ' ', code: 'Space' });
    
    expect(mockOnTabChange).toHaveBeenCalledWith('history');
  });

  it('does not activate tab on other keys', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const historyTab = screen.getByTestId('nav-history');
    fireEvent.keyDown(historyTab, { key: 'Escape', code: 'Escape' });
    
    expect(mockOnTabChange).not.toHaveBeenCalled();
  });

  it('renders with correct ARIA attributes', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const navigation = screen.getByRole('navigation');
    expect(navigation).toBeInTheDocument();
    
    const calculatorTab = screen.getByTestId('nav-calculator');
    const historyTab = screen.getByTestId('nav-history');
    const analysisTab = screen.getByTestId('nav-analysis');
    
    expect(calculatorTab).toHaveAttribute('role', 'tab');
    expect(historyTab).toHaveAttribute('role', 'tab');
    expect(analysisTab).toHaveAttribute('role', 'tab');
    expect(calculatorTab).toHaveAttribute('aria-selected', 'true');
    expect(historyTab).toHaveAttribute('aria-selected', 'false');
    expect(analysisTab).toHaveAttribute('aria-selected', 'false');
  });

  it('applies correct CSS classes for styling', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const navigation = screen.getByRole('navigation');
    expect(navigation).toHaveClass('navigation');
    
    const calculatorTab = screen.getByTestId('nav-calculator');
    const historyTab = screen.getByTestId('nav-history');
    const analysisTab = screen.getByTestId('nav-analysis');
    
    expect(calculatorTab).toHaveClass('navTab', 'active');
    expect(historyTab).toHaveClass('navTab');
    expect(analysisTab).toHaveClass('navTab');
  });

  it('maintains tab order for accessibility', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const tabs = screen.getAllByRole('tab');
    expect(tabs).toHaveLength(3);
    
    // Verify tab order
    expect(tabs[0]).toHaveTextContent('Calculator');
    expect(tabs[1]).toHaveTextContent('History');
    expect(tabs[2]).toHaveTextContent('Analysis');
  });

  it('prevents default behavior on Space key to avoid scrolling', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const historyTab = screen.getByTestId('nav-history');
    
    // Test that the component handles space key correctly by checking onTabChange is called
    fireEvent.keyDown(historyTab, { key: ' ', code: 'Space' });
    
    // Verify that the onTabChange was called (which means preventDefault was also called)
    expect(mockOnTabChange).toHaveBeenCalledWith('history');
  });

  it('handles multiple rapid clicks correctly', async () => {
    const user = userEvent.setup();
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const historyTab = screen.getByTestId('nav-history');
    
    // Click multiple times rapidly
    await user.click(historyTab);
    await user.click(historyTab);
    await user.click(historyTab);
    
    expect(mockOnTabChange).toHaveBeenCalledTimes(3);
    expect(mockOnTabChange).toHaveBeenCalledWith('history');
  });

  it('works with all available tabs', async () => {
    const user = userEvent.setup();
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    // Test each tab
    const historyTab = screen.getByTestId('nav-history');
    await user.click(historyTab);
    expect(mockOnTabChange).toHaveBeenCalledWith('history');
    
    const analysisTab = screen.getByTestId('nav-analysis');
    await user.click(analysisTab);
    expect(mockOnTabChange).toHaveBeenCalledWith('analysis');
    
    const calculatorTab = screen.getByTestId('nav-calculator');
    await user.click(calculatorTab);
    expect(mockOnTabChange).toHaveBeenCalledWith('calculator');
  });

  it('displays clear data button', () => {
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const clearButton = screen.getByTestId('clear-data-button');
    expect(clearButton).toBeInTheDocument();
    expect(clearButton).toHaveTextContent('Clear All Data');
  });

  it('calls onTabChange with clear when clear button is clicked', async () => {
    const user = userEvent.setup();
    render(<Navigation activeTab="calculator" onTabChange={mockOnTabChange} />);
    
    const clearButton = screen.getByTestId('clear-data-button');
    await user.click(clearButton);
    
    expect(mockOnTabChange).toHaveBeenCalledWith('clear');
  });
});