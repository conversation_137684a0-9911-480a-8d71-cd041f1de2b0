import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import MilestoneProjectionsTab from '../components/MilestoneProjectionsTab';

// Mock fetch
global.fetch = jest.fn();

const mockProjectionsData = {
  target: 1500000,
  currentValue: 786000,
  startDate: 'Jul 2025',
  projections: [
    {
      scenario: 'Optimistic',
      rate: 0.0427,
      description: '4.27% annual return',
      contributions: [
        { monthlyContrib: 6250, hitDate: 'Oct 2026', months: 15, delayVsBaseline: '–' },
        { monthlyContrib: 5260, hitDate: 'Nov 2026', months: 16, delayVsBaseline: '+1' },
        { monthlyContrib: 4200, hitDate: 'Jan 2027', months: 18, delayVsBaseline: '+3' }
      ]
    },
    {
      scenario: 'Baseline',
      rate: 0.0239,
      description: '2.39% annual return',
      contributions: [
        { monthlyContrib: 6250, hitDate: 'Jun 2027', months: 23, delayVsBaseline: '–' },
        { monthlyContrib: 5260, hitDate: 'Aug 2027', months: 25, delayVsBaseline: '+2' },
        { monthlyContrib: 4200, hitDate: 'Oct 2027', months: 27, delayVsBaseline: '+4' }
      ]
    }
  ]
};

describe('MilestoneProjectionsTab', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.Mock).mockClear();
  });

  describe('Loading State', () => {
    it('should show loading state initially', () => {
      (fetch as jest.Mock).mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          ok: true,
          json: () => Promise.resolve({ success: true, data: mockProjectionsData })
        }), 100))
      );

      render(<MilestoneProjectionsTab />);
      
      expect(screen.getByText('Loading milestone projections...')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should show error message on API failure', async () => {
      (fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: false,
          error: 'Insufficient data for analysis'
        })
      });

      render(<MilestoneProjectionsTab />);

      await waitFor(() => {
        expect(screen.getByText('Insufficient data for analysis')).toBeInTheDocument();
      });
    });

    it('should show retry button on error', async () => {
      (fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: false,
          error: 'Network error'
        })
      });

      render(<MilestoneProjectionsTab />);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /Retry/i })).toBeInTheDocument();
      });
    });

    it('should retry on retry button click', async () => {
      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: false, error: 'Network error' })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: mockProjectionsData })
        });

      render(<MilestoneProjectionsTab />);

      await waitFor(() => {
        expect(screen.getByText('Network error')).toBeInTheDocument();
      });

      const retryButton = screen.getByRole('button', { name: /Retry/i });
      fireEvent.click(retryButton);

      await waitFor(() => {
        expect(screen.getByText('Milestone Projections @ Three Contribution Levels')).toBeInTheDocument();
      });
    });
  });

  describe('Successful Data Display', () => {
    beforeEach(async () => {
      (fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true, data: mockProjectionsData })
      });

      render(<MilestoneProjectionsTab />);

      await waitFor(() => {
        expect(screen.getByText('Milestone Projections @ Three Contribution Levels')).toBeInTheDocument();
      });
    });

    it('should display title and subtitle correctly', () => {
      expect(screen.getByText('Milestone Projections @ Three Contribution Levels')).toBeInTheDocument();
      // Use a more flexible matcher for the subtitle since it might be broken up by elements
      expect(screen.getByText((content, element) => {
        return content.includes('Start =') && content.includes('balance') && content.includes('Target =');
      })).toBeInTheDocument();
    });

    it('should display controls', () => {
      expect(screen.getByLabelText(/Target Amount/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/Monthly Contributions/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Update Projections/i })).toBeInTheDocument();
    });

    it('should display table headers', () => {
      expect(screen.getByText('Return scenario')).toBeInTheDocument();
      expect(screen.getByText('Monthly contrib')).toBeInTheDocument();
      expect(screen.getByText('Hit date')).toBeInTheDocument();
      expect(screen.getByText('Months')).toBeInTheDocument();
      expect(screen.getByText(/Delay vs.*6 250/)).toBeInTheDocument();
    });

    it('should display scenario data', () => {
      expect(screen.getByText('Optimistic 4.27 %')).toBeInTheDocument();
      expect(screen.getByText('Baseline 2.39 %')).toBeInTheDocument();
    });

    it('should display contribution amounts with proper formatting', () => {
      expect(screen.getAllByText('$6 250')).toHaveLength(2); // Appears in 2 scenarios
      expect(screen.getAllByText('$5 260')).toHaveLength(2); // Appears in 2 scenarios
      expect(screen.getAllByText('$4 200')).toHaveLength(2); // Appears in 2 scenarios
    });

    it('should display hit dates', () => {
      expect(screen.getByText('Oct 2026')).toBeInTheDocument();
      expect(screen.getByText('Nov 2026')).toBeInTheDocument();
      expect(screen.getByText('Jun 2027')).toBeInTheDocument();
    });

    it('should display months and delays', () => {
      expect(screen.getByText('15')).toBeInTheDocument();
      expect(screen.getByText('16')).toBeInTheDocument();
      expect(screen.getByText('+1')).toBeInTheDocument();
      expect(screen.getByText('+3')).toBeInTheDocument();
      expect(screen.getAllByText('–')).toHaveLength(2); // Baseline delays
    });

    it('should display insights section', () => {
      expect(screen.getByText('Key Insights')).toBeInTheDocument();
      expect(screen.getByText(/Higher monthly contributions significantly reduce/)).toBeInTheDocument();
      expect(screen.getByText(/Market return scenarios have a major impact/)).toBeInTheDocument();
    });
  });

  describe('Controls Interaction', () => {
    beforeEach(async () => {
      (fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true, data: mockProjectionsData })
      });

      render(<MilestoneProjectionsTab />);

      await waitFor(() => {
        expect(screen.getByText('Milestone Projections @ Three Contribution Levels')).toBeInTheDocument();
      });
    });

    it('should update target amount input', () => {
      const targetInput = screen.getByLabelText(/Target Amount/i) as HTMLInputElement;
      
      fireEvent.change(targetInput, { target: { value: '2.0' } });
      
      expect(targetInput.value).toBe('2.0');
    });

    it('should update contributions input', () => {
      const contributionsInput = screen.getByLabelText(/Monthly Contributions/i) as HTMLInputElement;
      
      fireEvent.change(contributionsInput, { target: { value: '5000,6000,7000' } });
      
      expect(contributionsInput.value).toBe('5000,6000,7000');
    });

    it('should call API with updated parameters on refresh', async () => {
      const targetInput = screen.getByLabelText(/Target Amount/i);
      const contributionsInput = screen.getByLabelText(/Monthly Contributions/i);
      const updateButton = screen.getByRole('button', { name: /Update Projections/i });

      fireEvent.change(targetInput, { target: { value: '2.0' } });
      fireEvent.change(contributionsInput, { target: { value: '5000,6000,7000' } });
      
      (fetch as jest.Mock).mockClear();
      (fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true, data: mockProjectionsData })
      });

      fireEvent.click(updateButton);

      await waitFor(() => {
        expect(fetch).toHaveBeenCalledWith(
          expect.stringContaining('target=2000000&contributions=5000,6000,7000')
        );
      });
    });

    it('should show loading state during update', async () => {
      (fetch as jest.Mock).mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          ok: true,
          json: () => Promise.resolve({ success: true, data: mockProjectionsData })
        }), 100))
      );

      const updateButton = screen.getByRole('button', { name: /Update Projections/i });
      fireEvent.click(updateButton);

      expect(screen.getByText('Loading...')).toBeInTheDocument();
      expect(updateButton).toBeDisabled();
    });
  });

  describe('Currency Formatting', () => {
    beforeEach(async () => {
      (fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true, data: mockProjectionsData })
      });

      render(<MilestoneProjectionsTab />);

      await waitFor(() => {
        expect(screen.getByText('Milestone Projections @ Three Contribution Levels')).toBeInTheDocument();
      });
    });

    it('should format currency values with spaces instead of commas', () => {
      // Check that contributions are formatted as "$6 250" not "$6,250"
      expect(screen.getAllByText('$6 250')).toHaveLength(2); // Appears in 2 scenarios
      expect(screen.getAllByText('$5 260')).toHaveLength(2); // Appears in 2 scenarios
      expect(screen.getAllByText('$4 200')).toHaveLength(2); // Appears in 2 scenarios
    });

    it('should format percentages correctly', () => {
      expect(screen.getByText('Optimistic 4.27 %')).toBeInTheDocument();
      expect(screen.getByText('Baseline 2.39 %')).toBeInTheDocument();
    });
  });
});
