import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import DataClearingTab from '../components/DataClearingTab';
import { apiService } from '../services/apiService';

// Mock the API service
jest.mock('../services/apiService', () => ({
  apiService: {
    resetData: jest.fn(),
  },
}));

// Mock fetch for the clear-and-setup endpoint
global.fetch = jest.fn();

const mockOnDataCleared = jest.fn();

describe('DataClearingTab', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.Mock).mockClear();
  });

  describe('Initial Confirmation Step', () => {
    it('should render confirmation step initially', () => {
      render(<DataClearingTab onDataCleared={mockOnDataCleared} />);
      
      expect(screen.getByRole('heading', { name: 'Clear All Data' })).toBeInTheDocument();
      expect(screen.getByText(/This will permanently delete/)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Clear All Data/i })).toBeInTheDocument();
    });

    it('should show loading state when clearing data', async () => {
      (apiService.resetData as jest.Mock).mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ success: true }), 100))
      );

      render(<DataClearingTab onDataCleared={mockOnDataCleared} />);
      
      const clearButton = screen.getByRole('button', { name: /Clear All Data/i });
      fireEvent.click(clearButton);

      expect(screen.getByText('Clearing...')).toBeInTheDocument();
      expect(clearButton).toBeDisabled();
    });

    it('should handle clear data error', async () => {
      (apiService.resetData as jest.Mock).mockResolvedValue({
        success: false,
        error: 'Failed to clear data'
      });

      render(<DataClearingTab onDataCleared={mockOnDataCleared} />);
      
      const clearButton = screen.getByRole('button', { name: /Clear All Data/i });
      fireEvent.click(clearButton);

      await waitFor(() => {
        expect(screen.getByText('Failed to clear data')).toBeInTheDocument();
      });
    });

    it('should proceed to setup step on successful clear', async () => {
      (apiService.resetData as jest.Mock).mockResolvedValue({ success: true });

      render(<DataClearingTab onDataCleared={mockOnDataCleared} />);
      
      const clearButton = screen.getByRole('button', { name: /Clear All Data/i });
      fireEvent.click(clearButton);

      await waitFor(() => {
        expect(screen.getByText('Initial Setup')).toBeInTheDocument();
      });
    });
  });

  describe('Setup Step', () => {
    beforeEach(async () => {
      (apiService.resetData as jest.Mock).mockResolvedValue({ success: true });
      
      render(<DataClearingTab onDataCleared={mockOnDataCleared} />);
      
      const clearButton = screen.getByRole('button', { name: /Clear All Data/i });
      fireEvent.click(clearButton);

      await waitFor(() => {
        expect(screen.getByText('Initial Setup')).toBeInTheDocument();
      });
    });

    it('should render setup form', () => {
      expect(screen.getByLabelText(/current portfolio value/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/investing per month/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Complete Setup/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Cancel/i })).toBeInTheDocument();
    });

    it('should show confirmation when values are entered', () => {
      const portfolioInput = screen.getByLabelText(/current portfolio value/i);
      const contributionInput = screen.getByLabelText(/investing per month/i);

      fireEvent.change(portfolioInput, { target: { value: '800000' } });
      fireEvent.change(contributionInput, { target: { value: '6250' } });

      expect(screen.getByText('✓ Portfolio value: $800,000')).toBeInTheDocument();
      expect(screen.getByText('✓ Monthly contribution: $6,250')).toBeInTheDocument();
    });

    it('should disable setup button when fields are empty', () => {
      const setupButton = screen.getByRole('button', { name: /Complete Setup/i });
      expect(setupButton).toBeDisabled();
    });

    it('should enable setup button when both fields are filled', () => {
      const portfolioInput = screen.getByLabelText(/current portfolio value/i);
      const contributionInput = screen.getByLabelText(/investing per month/i);

      fireEvent.change(portfolioInput, { target: { value: '800000' } });
      fireEvent.change(contributionInput, { target: { value: '6250' } });

      const setupButton = screen.getByRole('button', { name: /Complete Setup/i });
      expect(setupButton).not.toBeDisabled();
    });

    it('should show error for invalid values', async () => {
      const portfolioInput = screen.getByLabelText(/current portfolio value/i);
      const contributionInput = screen.getByLabelText(/investing per month/i);
      const setupButton = screen.getByRole('button', { name: /Complete Setup/i });

      fireEvent.change(portfolioInput, { target: { value: '-1000' } });
      fireEvent.change(contributionInput, { target: { value: '6250' } });
      fireEvent.click(setupButton);

      await waitFor(() => {
        expect(screen.getByText('Please enter valid positive numbers')).toBeInTheDocument();
      });
    });

    it('should complete setup successfully', async () => {
      (fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: { portfolioValue: 800000, monthlyContribution: 6250 }
        })
      });

      const portfolioInput = screen.getByLabelText(/current portfolio value/i);
      const contributionInput = screen.getByLabelText(/investing per month/i);
      const setupButton = screen.getByRole('button', { name: /Complete Setup/i });

      fireEvent.change(portfolioInput, { target: { value: '800000' } });
      fireEvent.change(contributionInput, { target: { value: '6250' } });
      fireEvent.click(setupButton);

      await waitFor(() => {
        expect(screen.getByText('Setup Complete! 🎉')).toBeInTheDocument();
      });

      expect(mockOnDataCleared).toHaveBeenCalled();
    });

    it('should handle setup error', async () => {
      (fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: false,
          error: 'Setup failed'
        })
      });

      const portfolioInput = screen.getByLabelText(/current portfolio value/i);
      const contributionInput = screen.getByLabelText(/investing per month/i);
      const setupButton = screen.getByRole('button', { name: /Complete Setup/i });

      fireEvent.change(portfolioInput, { target: { value: '800000' } });
      fireEvent.change(contributionInput, { target: { value: '6250' } });
      fireEvent.click(setupButton);

      await waitFor(() => {
        expect(screen.getByText('Setup failed')).toBeInTheDocument();
      });
    });

    it('should return to confirmation step on cancel', () => {
      const cancelButton = screen.getByRole('button', { name: /Cancel/i });
      fireEvent.click(cancelButton);

      expect(screen.getByRole('heading', { name: 'Clear All Data' })).toBeInTheDocument();
    });
  });

  describe('Complete Step', () => {
    beforeEach(async () => {
      (apiService.resetData as jest.Mock).mockResolvedValue({ success: true });
      (fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: { portfolioValue: 800000, monthlyContribution: 6250 }
        })
      });

      render(<DataClearingTab onDataCleared={mockOnDataCleared} />);
      
      // Go through the flow
      const clearButton = screen.getByRole('button', { name: /Clear All Data/i });
      fireEvent.click(clearButton);

      await waitFor(() => {
        expect(screen.getByText('Initial Setup')).toBeInTheDocument();
      });

      const portfolioInput = screen.getByLabelText(/current portfolio value/i);
      const contributionInput = screen.getByLabelText(/investing per month/i);
      const setupButton = screen.getByRole('button', { name: /Complete Setup/i });

      fireEvent.change(portfolioInput, { target: { value: '800000' } });
      fireEvent.change(contributionInput, { target: { value: '6250' } });
      fireEvent.click(setupButton);

      await waitFor(() => {
        expect(screen.getByText('Setup Complete! 🎉')).toBeInTheDocument();
      });
    });

    it('should show completion message with values', () => {
      expect(screen.getByText('Portfolio value: $800,000')).toBeInTheDocument();
      expect(screen.getByText('Monthly contribution: $6,250')).toBeInTheDocument();
    });

    it('should allow starting over', () => {
      const startOverButton = screen.getByRole('button', { name: /Clear Data Again/i });
      fireEvent.click(startOverButton);

      expect(screen.getByRole('heading', { name: 'Clear All Data' })).toBeInTheDocument();
    });
  });
});
