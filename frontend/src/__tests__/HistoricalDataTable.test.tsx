import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import HistoricalDataTable from '../components/HistoricalDataTable';

// Mock TooltipInfo component
jest.mock('../components/TooltipInfo', () => {
  return function MockTooltipInfo({ children, content, title }: { children: React.ReactNode; content: string; title?: string }) {
    return (
      <div data-testid="tooltip-info" data-content={content} data-title={title}>
        {children}
      </div>
    );
  };
});

describe('HistoricalDataTable', () => {
  const mockEntries = [
    {
      id: 'portfolio-1',
      date: new Date('2023-01-15'),
      type: 'portfolio' as const,
      data: {
        assetType: 'stocks' as const,
        value: 10000,
        description: 'Stock Investment'
      },
      summary: 'Stock Investment - $10,000',
      value: 10000
    },
    {
      id: 'payslip-1',
      date: new Date('2023-01-10'),
      type: 'payslip' as const,
      data: {
        grossSalary: 5000,
        netSalary: 4000,
        taxDeductions: 800,
        benefits: 200,
        payPeriod: 'monthly' as const
      },
      summary: 'Monthly Salary - $5,000 gross',
      value: 5000
    },
    {
      id: 'portfolio-2',
      date: new Date('2023-01-05'),
      type: 'portfolio' as const,
      data: {
        assetType: 'bonds' as const,
        value: 5000,
        description: 'Bond Investment'
      },
      summary: 'Bond Investment - $5,000',
      value: 5000
    },
    {
      id: 'analysis-1',
      date: new Date('2023-01-01'),
      type: 'analysis' as const,
      data: {
        totalAssets: 50000,
        netWorth: 45000,
        monthlyIncome: 5000,
        annualIncome: 60000,
        savingsRate: 0.2,
        assetBreakdown: {
          stocks: 30000,
          bonds: 15000,
          savings: 5000
        }
      },
      summary: 'Wealth Analysis - $45,000 net worth',
      value: 45000
    }
  ];

  it('renders table with historical data', () => {
    render(<HistoricalDataTable entries={mockEntries} />);
    
    expect(screen.getByRole('table')).toBeInTheDocument();
    expect(screen.getByText('Stock Investment - $10,000')).toBeInTheDocument();
    expect(screen.getByText('Monthly Salary - $5,000 gross')).toBeInTheDocument();
    expect(screen.getByText('Bond Investment - $5,000')).toBeInTheDocument();
    expect(screen.getByText('Wealth Analysis - $45,000 net worth')).toBeInTheDocument();
  });

  it('displays correct table headers', () => {
    render(<HistoricalDataTable entries={mockEntries} />);
    
    expect(screen.getByText('Date')).toBeInTheDocument();
    expect(screen.getByText('Type')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByText('Value')).toBeInTheDocument();
    expect(screen.getByText('Category')).toBeInTheDocument();
  });

  it('formats dates correctly', () => {
    render(<HistoricalDataTable entries={mockEntries} />);
    
    expect(screen.getByText('Jan 15, 2023')).toBeInTheDocument();
    expect(screen.getByText('Jan 10, 2023')).toBeInTheDocument();
    expect(screen.getByText('Jan 5, 2023')).toBeInTheDocument();
    expect(screen.getByText('Jan 1, 2023')).toBeInTheDocument();
  });

  it('formats currency values correctly', () => {
    render(<HistoricalDataTable entries={mockEntries} />);
    
    expect(screen.getByText('$10,000')).toBeInTheDocument();
    expect(screen.getByText('$8,000')).toBeInTheDocument();
    expect(screen.getByText('$5,000')).toBeInTheDocument();
    expect(screen.getByText('$2,000')).toBeInTheDocument();
  });

  it('sorts by date descending by default', () => {
    render(<HistoricalDataTable entries={mockEntries} />);
    
    const rows = screen.getAllByRole('row');
    // First row is header, so data rows start from index 1
    expect(rows[1]).toHaveTextContent('Jan 15, 2023'); // Most recent first
    expect(rows[2]).toHaveTextContent('Jan 10, 2023');
    expect(rows[3]).toHaveTextContent('Jan 5, 2023');
    expect(rows[4]).toHaveTextContent('Jan 1, 2023'); // Oldest last
  });

  it('allows sorting by different columns', async () => {
    render(<HistoricalDataTable entries={mockEntries} />);
    
    const valueHeader = screen.getByText('Value');
    fireEvent.click(valueHeader);
    
    await waitFor(() => {
      const rows = screen.getAllByRole('row');
      // Should be sorted by value ascending
      expect(rows[1]).toHaveTextContent('$2,000');
      expect(rows[4]).toHaveTextContent('$10,000');
    });
  });

  it('filters by entry type', async () => {
    render(<HistoricalDataTable entries={mockEntries} />);
    
    const portfolioFilter = screen.getByText('Portfolio (2)');
    fireEvent.click(portfolioFilter);
    
    await waitFor(() => {
      expect(screen.getByText('Stock Investment')).toBeInTheDocument();
      expect(screen.getByText('Bond Investment')).toBeInTheDocument();
      expect(screen.queryByText('Monthly Salary')).not.toBeInTheDocument();
      expect(screen.queryByText('Bonus Payment')).not.toBeInTheDocument();
    });
  });

  it('filters by payslip type', async () => {
    render(<HistoricalDataTable entries={mockEntries} />);
    
    const payslipFilter = screen.getByText('Payslip (2)');
    fireEvent.click(payslipFilter);
    
    await waitFor(() => {
      expect(screen.getByText('Monthly Salary')).toBeInTheDocument();
      expect(screen.getByText('Bonus Payment')).toBeInTheDocument();
      expect(screen.queryByText('Stock Investment')).not.toBeInTheDocument();
      expect(screen.queryByText('Bond Investment')).not.toBeInTheDocument();
    });
  });

  it('resets filter when All is clicked', async () => {
    render(<HistoricalDataTable entries={mockEntries} />);
    
    // First filter by portfolio
    const portfolioFilter = screen.getByText('Portfolio (2)');
    fireEvent.click(portfolioFilter);
    
    await waitFor(() => {
      expect(screen.queryByText('Monthly Salary')).not.toBeInTheDocument();
    });
    
    // Then reset to show all
    const allFilter = screen.getByText('All (4)');
    fireEvent.click(allFilter);
    
    await waitFor(() => {
      expect(screen.getByText('Monthly Salary')).toBeInTheDocument();
      expect(screen.getByText('Stock Investment')).toBeInTheDocument();
    });
  });

  it('handles empty data gracefully', () => {
    render(<HistoricalDataTable entries={[]} />);
    
    expect(screen.getByRole('table')).toBeInTheDocument();
    expect(screen.getByText('No historical data available')).toBeInTheDocument();
  });

  it('shows correct filter counts', () => {
    render(<HistoricalDataTable entries={mockEntries} />);
    
    expect(screen.getByText('All (4)')).toBeInTheDocument();
    expect(screen.getByText('Portfolio (2)')).toBeInTheDocument();
    expect(screen.getByText('Payslip (1)')).toBeInTheDocument();
  });

  it('handles date range filtering', async () => {
    render(<HistoricalDataTable entries={mockEntries} />);

    const dateRangeFilter = screen.getByTestId('date-range-filter');

    // Test changing the date range filter to 30 days
    fireEvent.change(dateRangeFilter, { target: { value: '30d' } });

    await waitFor(() => {
      expect(dateRangeFilter).toHaveValue('30d');
      // All entries should still be visible since they're within 30 days of each other
      expect(screen.getByText('Stock Investment - $10,000')).toBeInTheDocument();
      expect(screen.getByText('Monthly Salary - $5,000 gross')).toBeInTheDocument();
    });
  });

  it('supports date range filtering', async () => {
    render(<HistoricalDataTable entries={mockEntries} />);

    const dateRangeFilter = screen.getByTestId('date-range-filter');

    // Test changing the date range filter
    fireEvent.change(dateRangeFilter, { target: { value: '30d' } });

    await waitFor(() => {
      expect(dateRangeFilter).toHaveValue('30d');
    });

    // Reset to all time
    fireEvent.change(dateRangeFilter, { target: { value: 'all' } });

    await waitFor(() => {
      expect(dateRangeFilter).toHaveValue('all');
    });
  });

  it('provides export functionality', () => {
    const mockOnExport = jest.fn();
    render(<HistoricalDataTable entries={mockEntries} onExport={mockOnExport} />);

    const exportButton = screen.getByText('📎 Export');
    expect(exportButton).toBeInTheDocument();

    fireEvent.click(exportButton);
    expect(mockOnExport).toHaveBeenCalledTimes(1);
  });

  it('handles sorting in both directions', async () => {
    render(<HistoricalDataTable entries={mockEntries} />);
    
    const valueHeader = screen.getByText('Value');
    
    // First click - ascending
    fireEvent.click(valueHeader);
    await waitFor(() => {
      const rows = screen.getAllByRole('row');
      expect(rows[1]).toHaveTextContent('$5,000');
    });
    
    // Second click - descending
    fireEvent.click(valueHeader);
    await waitFor(() => {
      const rows = screen.getAllByRole('row');
      expect(rows[1]).toHaveTextContent('$45,000');
    });
  });

  it('displays tooltips for column headers', () => {
    render(<HistoricalDataTable entries={mockEntries} />);
    
    const tooltips = screen.getAllByTestId('tooltip-info');
    expect(tooltips.length).toBeGreaterThan(0);
    
    // Check that tooltips have meaningful content
    tooltips.forEach(tooltip => {
      expect(tooltip).toHaveAttribute('data-content');
      expect(tooltip.getAttribute('data-content')).toBeTruthy();
    });
  });

  it('handles large datasets efficiently', () => {
    const largeDataset = Array.from({ length: 10 }, (_, i) => {
      const isPortfolio = i % 2 === 0;
      const value = (i + 1) * 100;

      return {
        id: `entry-${i + 1}`,
        date: new Date(2023, 0, i + 1),
        type: isPortfolio ? 'portfolio' as const : 'payslip' as const,
        data: isPortfolio
          ? {
              assetType: 'stocks' as const,
              value: value,
              description: `Entry ${i + 1}`
            }
          : {
              grossSalary: value,
              netSalary: value * 0.8,
              taxDeductions: value * 0.15,
              benefits: value * 0.05,
              payPeriod: 'monthly' as const
            },
        summary: `Entry ${i + 1} - $${value.toLocaleString()}`,
        value: value
      };
    });

    render(<HistoricalDataTable entries={largeDataset} />);

    expect(screen.getByRole('table')).toBeInTheDocument();
    expect(screen.getByText('Entry 1 - $100')).toBeInTheDocument();
  });

  it('maintains filter state during sorting', async () => {
    render(<HistoricalDataTable entries={mockEntries} />);
    
    // Apply portfolio filter
    await waitFor(() => {
      expect(screen.getByText(/Portfolio \(2\)/)).toBeInTheDocument();
    });
    const portfolioFilter = screen.getByText(/Portfolio \(2\)/);
    fireEvent.click(portfolioFilter);
    
    await waitFor(() => {
      expect(screen.queryByText('Monthly Salary')).not.toBeInTheDocument();
    });
    
    // Sort by value
    const valueHeader = screen.getByText('Value');
    fireEvent.click(valueHeader);
    
    await waitFor(() => {
      // Should still be filtered to portfolio only
      expect(screen.getByText('Stock Investment - $10,000')).toBeInTheDocument();
      expect(screen.getByText('Bond Investment - $5,000')).toBeInTheDocument();
      expect(screen.queryByText('Monthly Salary')).not.toBeInTheDocument();
    });
  });

  it('handles different data types correctly', () => {
    const mixedEntries = [
      {
        id: 'portfolio-3',
        date: new Date('2023-01-15'),
        type: 'portfolio' as const,
        data: {
          assetType: 'real_estate' as const,
          value: 150000,
          description: 'Real Estate'
        },
        summary: 'Real Estate - $150,000',
        value: 150000
      },
      {
        id: 'payslip-2',
        date: new Date('2023-01-10'),
        type: 'payslip' as const,
        data: {
          grossSalary: 500,
          netSalary: 400,
          taxDeductions: 80,
          benefits: 20,
          payPeriod: 'monthly' as const
        },
        summary: 'Overtime Pay - $500',
        value: 500
      }
    ];
    
    render(<HistoricalDataTable entries={mixedEntries} />);
    
    expect(screen.getByText('Real Estate - $150,000')).toBeInTheDocument();
    expect(screen.getByText('Overtime Pay - $500')).toBeInTheDocument();
    expect(screen.getByText('$150,000')).toBeInTheDocument();
    expect(screen.getByText('$500')).toBeInTheDocument();
  });
});