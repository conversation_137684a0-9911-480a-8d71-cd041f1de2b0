import React, { useState, useRef, useEffect } from 'react';
import styles from './TooltipInfo.module.css';

interface TooltipInfoProps {
  children: React.ReactNode;
  content: string | React.ReactNode;
  title?: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
}

const TooltipInfo: React.FC<TooltipInfoProps> = ({
  children,
  content,
  title,
  position = 'top',
  delay = process.env.NODE_ENV === 'test' ? 0 : 300
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  const showTooltip = (event: React.MouseEvent) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      const rect = triggerRef.current?.getBoundingClientRect();
      if (rect) {
        let x = event.clientX;
        let y = event.clientY;
        
        // Adjust position based on viewport
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const tooltipWidth = 300; // estimated width
        const tooltipHeight = 150; // estimated height
        
        switch (position) {
          case 'top':
            x = rect.left + rect.width / 2;
            y = rect.top - 10;
            break;
          case 'bottom':
            x = rect.left + rect.width / 2;
            y = rect.bottom + 10;
            break;
          case 'left':
            x = rect.left - 10;
            y = rect.top + rect.height / 2;
            break;
          case 'right':
            x = rect.right + 10;
            y = rect.top + rect.height / 2;
            break;
        }
        
        // Keep tooltip within viewport
        if (x + tooltipWidth > viewportWidth) {
          x = viewportWidth - tooltipWidth - 20;
        }
        if (x < 20) {
          x = 20;
        }
        if (y + tooltipHeight > viewportHeight) {
          y = viewportHeight - tooltipHeight - 20;
        }
        if (y < 20) {
          y = 20;
        }
        
        setTooltipPosition({ x, y });
        setIsVisible(true);
      }
    }, delay);
  };

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsVisible(false);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <>
      <div
        ref={triggerRef}
        className={styles.tooltipTrigger}
        onMouseEnter={showTooltip}
        onMouseLeave={hideTooltip}
        onMouseMove={showTooltip}
        data-testid="tooltip-trigger"
      >
        {children}
      </div>
      
      {isVisible && (
        <div
          ref={tooltipRef}
          className={`${styles.tooltip} ${styles[position]}`}
          style={{
            left: tooltipPosition.x,
            top: tooltipPosition.y,
          }}
          data-testid="tooltip-content"
        >
          <div className={styles.tooltipGlass}>
            <div className={styles.tooltipContent}>
              {title && (
                <div className={styles.tooltipTitle}>{title}</div>
              )}
              <div className={styles.tooltipBody}>
                {content}
              </div>
            </div>
            <div className={styles.tooltipArrow}></div>
          </div>
        </div>
      )}
    </>
  );
};

export default TooltipInfo;