import React, { useState, useEffect } from 'react';
import { apiService } from '../services/apiService';
import styles from './FinancialProjectionTable.module.css';

interface ProjectionData {
  scenario: string;
  rate: number;
  description: string;
  contributions: {
    monthlyContrib: number;
    hitDate: string;
    months: number;
    delayVsBaseline: string;
  }[];
}

interface ProjectionResponse {
  target: number;
  currentValue: number;
  startDate: string;
  projections: ProjectionData[];
}

const FinancialProjectionTable: React.FC = () => {
  const [projectionData, setProjectionData] = useState<ProjectionResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchProjections();
  }, []);

  const fetchProjections = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Fetch milestone projections with specific parameters
      const response = await fetch('/api/analysis/milestone-projections?target=1500000&contributions=4200,5260,6250');
      
      if (!response.ok) {
        throw new Error('Failed to fetch projection data');
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch projections');
      }
      
      setProjectionData(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDelay = (delay: string): string => {
    if (delay === '–' || delay === '0') return '–';
    return delay.startsWith('+') ? delay : `+${delay}`;
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>Loading financial projections...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.error}>
          <h3>Error Loading Projections</h3>
          <p>{error}</p>
          <button onClick={fetchProjections} className={styles.retryButton}>
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!projectionData) {
    return (
      <div className={styles.container}>
        <div className={styles.error}>No projection data available</div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>Financial Projection Table</h2>
        <div className={styles.summary}>
          <p>
            <strong>Target:</strong> {formatCurrency(projectionData.target)} |{' '}
            <strong>Current Value:</strong> {formatCurrency(projectionData.currentValue)} |{' '}
            <strong>Start Date:</strong> {projectionData.startDate}
          </p>
        </div>
      </div>

      <div className={styles.tableContainer}>
        <table className={styles.projectionTable}>
          <thead>
            <tr>
              <th>Scenario</th>
              <th>Monthly Rate</th>
              <th>$4,200/mo</th>
              <th>$5,260/mo</th>
              <th>$6,250/mo</th>
            </tr>
            <tr className={styles.subHeader}>
              <th></th>
              <th></th>
              <th>Date (Delay)</th>
              <th>Date (Delay)</th>
              <th>Date (Delay)</th>
            </tr>
          </thead>
          <tbody>
            {projectionData.projections.map((projection, index) => (
              <tr key={index} className={styles.dataRow}>
                <td className={styles.scenarioCell}>
                  <div className={styles.scenarioName}>{projection.scenario}</div>
                  <div className={styles.scenarioRate}>{projection.description}</div>
                </td>
                <td className={styles.rateCell}>
                  {(projection.rate * 12 * 100).toFixed(1)}%/yr
                </td>
                {projection.contributions.map((contrib, contribIndex) => (
                  <td key={contribIndex} className={styles.contributionCell}>
                    <div className={styles.hitDate}>{contrib.hitDate}</div>
                    <div className={styles.delay}>
                      ({formatDelay(contrib.delayVsBaseline)} mo)
                    </div>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className={styles.footer}>
        <div className={styles.notes}>
          <h4>Notes:</h4>
          <ul>
            <li>Delays are calculated relative to the $6,250/month baseline contribution</li>
            <li>Projections are based on your current portfolio value and historical performance</li>
            <li>Actual results may vary due to market volatility and changing circumstances</li>
            <li>These are projections for planning purposes only, not investment advice</li>
          </ul>
        </div>
        
        <div className={styles.actions}>
          <button onClick={fetchProjections} className={styles.refreshButton}>
            Refresh Data
          </button>
        </div>
      </div>
    </div>
  );
};

export default FinancialProjectionTable;