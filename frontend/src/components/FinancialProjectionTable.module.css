.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON>o', sans-serif;
}

.header {
  margin-bottom: 24px;
}

.header h2 {
  color: #1a202c;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 12px;
}

.summary {
  background-color: #f7fafc;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #4299e1;
}

.summary p {
  margin: 0;
  color: #2d3748;
  font-size: 14px;
}

.tableContainer {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  margin-bottom: 24px;
}

.projectionTable {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.projectionTable thead {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.projectionTable th {
  padding: 16px 12px;
  text-align: center;
  font-weight: 600;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.subHeader {
  background: rgba(102, 126, 234, 0.8) !important;
}

.subHeader th {
  padding: 8px 12px;
  font-size: 11px;
  font-weight: 500;
}

.dataRow {
  border-bottom: 1px solid #e2e8f0;
  transition: background-color 0.15s ease;
}

.dataRow:hover {
  background-color: #f8fafc;
}

.dataRow:last-child {
  border-bottom: none;
}

.scenarioCell {
  padding: 16px 12px;
  text-align: left;
  width: 200px;
  background-color: #fafafa;
  border-right: 2px solid #e2e8f0;
}

.scenarioName {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
  font-size: 15px;
}

.scenarioRate {
  font-size: 12px;
  color: #718096;
  font-style: italic;
}

.rateCell {
  padding: 16px 12px;
  text-align: center;
  font-weight: 500;
  color: #4a5568;
  background-color: #f9f9f9;
  width: 100px;
}

.contributionCell {
  padding: 16px 12px;
  text-align: center;
  width: 140px;
}

.hitDate {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
  font-size: 14px;
}

.delay {
  font-size: 12px;
  color: #718096;
  font-weight: 500;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #718096;
}

.error {
  text-align: center;
  padding: 40px 20px;
  background-color: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 8px;
  color: #c53030;
}

.error h3 {
  margin: 0 0 12px 0;
  font-size: 18px;
}

.error p {
  margin: 0 0 16px 0;
  font-size: 14px;
}

.retryButton {
  background-color: #e53e3e;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.15s ease;
}

.retryButton:hover {
  background-color: #c53030;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
  margin-top: 24px;
}

.notes {
  flex: 1;
  background-color: #f7fafc;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #4299e1;
}

.notes h4 {
  margin: 0 0 12px 0;
  color: #2d3748;
  font-size: 16px;
  font-weight: 600;
}

.notes ul {
  margin: 0;
  padding-left: 20px;
  color: #4a5568;
}

.notes li {
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.5;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.refreshButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: transform 0.15s ease, box-shadow 0.15s ease;
  min-width: 140px;
}

.refreshButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 15px rgba(102, 126, 234, 0.3);
}

.refreshButton:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .tableContainer {
    overflow-x: auto;
  }
  
  .projectionTable {
    min-width: 600px;
  }
  
  .footer {
    flex-direction: column;
    align-items: stretch;
  }
  
  .actions {
    align-self: center;
  }
}

@media (max-width: 480px) {
  .header h2 {
    font-size: 24px;
  }
  
  .projectionTable th {
    padding: 12px 8px;
    font-size: 12px;
  }
  
  .contributionCell,
  .scenarioCell,
  .rateCell {
    padding: 12px 8px;
  }
  
  .scenarioCell {
    width: 160px;
  }
  
  .contributionCell {
    width: 120px;
  }
}