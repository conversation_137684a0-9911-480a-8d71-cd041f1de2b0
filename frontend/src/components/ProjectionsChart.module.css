.chartContainer {
  width: 100%;
  max-width: 900px;
}

.glassCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.cardHeader {
  padding: 24px 24px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.cardHeader h2 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  cursor: help;
}

.metricSelector {
  display: flex;
  gap: 8px;
  background: rgba(255, 255, 255, 0.05);
  padding: 4px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.metricButton {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.metricButton:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

.metricButton.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.chartContent {
  padding: 20px;
  position: relative;
}

.chart {
  width: 100%;
  height: auto;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  overflow: visible;
}

.hoverInfo {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
  pointer-events: none;
}

.hoverCard {
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  min-width: 200px;
}

.hoverCard h4 {
  color: #ffffff;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-align: center;
}

.hoverMetrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.hoverMetric {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.hoverLabel {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
  font-weight: 500;
}

.hoverValue {
  color: #ffffff;
  font-size: 0.9rem;
  font-weight: 600;
}

.chartLegend {
  padding: 0 24px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.legendItem {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: help;
}

.legendColor {
  width: 16px;
  height: 3px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.legendItem span {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  font-weight: 500;
}

.legendNote {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  font-style: italic;
}

@media (max-width: 768px) {
  .cardHeader {
    flex-direction: column;
    align-items: stretch;
  }
  
  .metricSelector {
    justify-content: center;
  }
  
  .metricButton {
    flex: 1;
    text-align: center;
  }
  
  .chartContent {
    padding: 15px;
  }
  
  .hoverInfo {
    position: relative;
    top: auto;
    right: auto;
    margin-top: 15px;
  }
  
  .chartLegend {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .metricSelector {
    flex-direction: column;
  }
  
  .cardHeader {
    padding: 16px 16px 0;
  }
  
  .chartContent {
    padding: 10px;
  }
  
  .chartLegend {
    padding: 0 16px 16px;
  }
}