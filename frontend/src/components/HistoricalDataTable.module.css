.tableContainer {
  width: 100%;
  max-width: 1200px;
}

.glassCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.cardHeader {
  padding: 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.headerTop {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.headerTop h2 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  cursor: help;
}

.exportButton {
  padding: 10px 20px;
  border: none;
  border-radius: 10px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.exportButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.filters {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
  align-items: end;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filterLabel {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-weight: 500;
}

.filterButtons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filterButton {
  padding: 8px 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.filterButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.3);
}

.filterButton.active {
  background: rgba(102, 126, 234, 0.3);
  border-color: rgba(102, 126, 234, 0.5);
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.filterSelect {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.filterSelect:focus {
  outline: none;
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(255, 255, 255, 0.1);
}

.filterSelect option {
  background: #2a2a2a;
  color: #ffffff;
}

.tableWrapper {
  overflow-x: auto;
  max-height: 600px;
  overflow-y: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.tableHead {
  background: rgba(255, 255, 255, 0.05);
  position: sticky;
  top: 0;
  z-index: 10;
}

.tableHeader {
  padding: 16px 12px;
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.tableHeader:hover {
  background: rgba(255, 255, 255, 0.08);
}

.headerContent {
  display: flex;
  align-items: center;
  gap: 4px;
}

.sortIcon {
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.tableHeader:hover .sortIcon {
  opacity: 1;
}

.tableBody {
}

.tableRow {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.2s ease;
  cursor: pointer;
}

.tableRow:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateX(2px);
}

.tableRow:last-child {
  border-bottom: none;
}

.tableCell {
  padding: 12px;
  color: rgba(255, 255, 255, 0.9);
  vertical-align: middle;
}

.dateCell {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
}

.typeCell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.typeIcon {
  font-size: 1.1rem;
}

.typeLabel {
  font-weight: 500;
  text-transform: capitalize;
}

.summaryCell {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.valueCell {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 600;
  color: #4ecdc4;
  text-align: right;
}

.actionsCell {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.actionButton {
  padding: 6px 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.actionButton:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  transform: scale(1.05);
}

.emptyState {
  padding: 60px 20px;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
}

.emptyIcon {
  font-size: 3rem;
  margin-bottom: 16px;
  opacity: 0.6;
}

.emptyState h3 {
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.emptyState p {
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 20px 0;
}

.clearFiltersButton {
  padding: 10px 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clearFiltersButton:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.cardFooter {
  padding: 16px 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
}

.footerStats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.footerStat {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .filters {
    flex-direction: column;
    gap: 16px;
  }
  
  .filterButtons {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .cardHeader {
    padding: 16px;
  }
  
  .headerTop {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .exportButton {
    align-self: center;
  }
  
  .tableWrapper {
    max-height: 400px;
  }
  
  .tableCell {
    padding: 8px;
  }
  
  .summaryCell {
    max-width: 150px;
  }
  
  .footerStats {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .cardHeader {
    padding: 12px;
  }
  
  .table {
    font-size: 0.8rem;
  }
  
  .tableHeader,
  .tableCell {
    padding: 6px;
  }
  
  .filterButton {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
  
  .summaryCell {
    max-width: 100px;
  }
}

/* Custom scrollbar */
.tableWrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.tableWrapper::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.tableWrapper::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

.tableWrapper::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}